# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start

```bash
# Environment setup
uv venv .venv --python=3.12
source .venv/bin/activate
uv sync

# Run the main service
python -m infra.endpoint.main

# Run tests
pytest tests/ -v

# Build Docker image
sh docker/build.sh
```

## Architecture Overview

**Intellix Data Science Agent** is a multi-agent system built on LangGraph that handles data science tasks through intelligent intent recognition, task planning, and execution. The system uses a three-layer architecture:

- **Client Layer**: Web UI, API clients, CLI tools
- **Application Layer**: AgentService with session management
- **Agent Layer**: IntentRecognizer → Planner → ExecutorAgent → FlexibleReactAgent
- **Tool Layer**: MCP-based tools (SQL generation, code generation, Jupyter execution, semantic search)
- **Data Layer**: PostgreSQL, Redis, Data Lake, Knowledge Base

## Key Components

### Core Services
- **AgentService** (`infra/datascience_agent/agent_service.py`): Main entry point, handles session management and orchestration
- **IntentRecognizer** (`infra/datascience_agent/agents/intent_recognizer/`): Three-layer intent recognition system
- **Planner** (`infra/datascience_agent/agents/planner/`): Task decomposition and planning
- **ExecutorAgent** (`infra/datascience_agent/agents/executor/`): Task execution via FlexibleReactAgent

### Infrastructure
- **MCPManager** (`infra/mcp/manager/`): Manages Model Context Protocol servers and tools
- **DataLoader** (`infra/datascience_agent/utils/data_loader.py`): Handles data loading and schema management
- **Memory System** (`infra/memory/`): Chat history, knowledge base, and semantic memory

### Common Utilities
- **Logger** (`common/logger/`): Structured logging with configurable levels
- **Metrics** (`common/metric/`): Prometheus metrics collection
- **Tracing** (`common/trace/`): OpenTelemetry-based distributed tracing
- **Database** (`common/database/`): PostgreSQL and Elasticsearch connections

## Development Commands

### Testing
```bash
# Run all tests
pytest tests/

# Run specific test module
pytest tests/agent/test_agent_interface_mock.py

# Run with coverage
pytest tests/ --cov=infra --cov-report=html

# Run async tests
pytest tests/ -v --asyncio-mode=auto
```

### Development Setup
```bash
# Create virtual environment
uv venv .venv --python=3.12
source .venv/bin/activate

# Install dependencies
uv sync

# Run linter
ruff check .
ruff format .

# Type checking
mypy infra/ --ignore-missing-imports
```

### Database Setup
```bash
# Initialize database
python -c "from common.database.database import init_db; init_db()"

# Run migrations
python -c "from infra.adapter import migrate; migrate()"
```

### Configuration

**Key config files:**
- `etc/config.yaml`: Main configuration (LLM endpoints, database, Redis)
- `common/share/config.py`: Configuration loading and validation
- `common/share/env.py`: Environment variables

**Environment Variables:**
- `EXECUTION_MODE`: local (development) or ray (production)
- `LOG_LEVEL`: DEBUG, INFO, WARNING, ERROR
- `OTEL_EXPORTER_OTLP_ENDPOINT`: OpenTelemetry endpoint for tracing
- `REDIS_URL`: Redis connection string

## Extension Points

### Adding New Intent Types
1. Define schema in `infra/datascience_agent/agents/intent_recognizer/intent_schemas.py`
2. Add examples in `prompt.py`
3. Update planning templates in `planner_prompts.py`

### Adding New Tools
1. Implement tool class extending `BaseTool` in `infra/datascience_agent/agents/tools/`
2. Register in tool registry
3. Add MCP server configuration

### Custom Execution Flow
1. Add new graph node in `infra/datascience_agent/graph_nodes.py`
2. Modify workflow in `infra/datascience_agent/graph_orchestrator.py`

## Debugging

### Session State Debugging
```python
from infra.datascience_agent.agent_service import AgentService

# Debug session state
agent_service = AgentService(mcp_manager)
state = agent_service.session_states.get("session_id")
print(f"Intent: {state.get('identified_intent_name')}")
print(f"Plan: {bool(state.get('current_plan'))}")
```

### Log Analysis
```bash
# Check agent logs
grep "IntentRecognizer" logs/data_agent.log | tail -20

# Check tool calls
grep "MCPManager" logs/data_agent.log | grep -i "call"

# Monitor performance
grep "duration" logs/data_agent.log | awk '{print $NF}' | sort -n
```

## Testing Patterns

### Mocking Strategies
- **MCPManager**: Use `unittest.mock.AsyncMock` for tool calls
- **LLM Client**: Mock `generate()` and `generate_stream()` methods
- **Database**: Use in-memory SQLite for unit tests

### Common Test Fixtures
```python
@pytest.fixture
def mock_mcp_manager():
    manager = AsyncMock()
    manager.call_tool.return_value = {"status": "success", "result": "mock_data"}
    return manager

@pytest.fixture
def agent_service(mock_mcp_manager):
    return AgentService(mock_mcp_manager)
```

## Performance Considerations

- **Async First**: All I/O operations use asyncio
- **Connection Pooling**: Redis and database connections are pooled
- **Caching**: Multi-level caching with Redis for frequently accessed data
- **Resource Limits**: Configurable timeouts for LLM calls and tool execution

## Security Notes

- Input validation at all entry points
- SQL injection prevention via parameterized queries
- Code execution sandboxing via Jupyter kernels
- Sensitive data redaction in logs
- API rate limiting and authentication

## Common Issues

1. **Tool Not Found**: Check MCP server registration and tool availability
2. **Session Timeout**: Adjust `chat_time_out` in config.yaml
3. **Memory Issues**: Monitor Redis usage and adjust connection limits
4. **LLM Rate Limits**: Implement exponential backoff for API calls
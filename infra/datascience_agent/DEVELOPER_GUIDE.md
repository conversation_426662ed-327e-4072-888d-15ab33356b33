# Intellix Data Science Agent 开发者指南

## 快速开始

### 环境设置

#### 1. 系统要求
- Python 3.12+
- Redis 6.0+
- PostgreSQL 13+
- Docker (可选)

#### 2. 依赖安装
```bash
# 使用uv管理Python环境
uv init intellix-ds-agent
cd intellix-ds-agent
uv venv .venv --python=3.12
source .venv/bin/activate  # macOS/Linux

# 安装依赖
uv add fastapi uvicorn
uv add langchain langgraph
uv add openai anthropic
uv add pandas numpy matplotlib seaborn
uv add redis psycopg2-binary
uv add pydantic typing-extensions
```

#### 3. 配置文件设置
```yaml
# etc/config.yaml
common:
  llm:
    model_name: "DeepSeek-V3-0324"
    base_url: "http://your-llm-endpoint/v1/"
    api_key: "your-api-key"
    temperature: 0.3
  
  database:
    host: "localhost"
    port: 5432
    database: "intellix_ds"
    username: "postgres"
    password: "password"
  
  redis:
    host: "localhost"
    port: 6379
    db: 0
```

### 基本使用示例

#### 1. 初始化服务
```python
from infra.datascience_agent.agent_service import AgentService
from infra.mcp.manager.mcp_manager import MCPManager

# 创建MCP管理器
mcp_manager = MCPManager()

# 初始化代理服务
agent_service = AgentService(mcp_manager)

# 使用示例
async def main():
    session_id = "demo_session_001"
    user_input = "请分析销售数据的趋势并预测下个月的销量"
    
    async for event in agent_service.stream_chat(user_input, session_id):
        print(f"Event: {event.type} - {event.content}")

# 运行
import asyncio
asyncio.run(main())
```

#### 2. 自定义工具开发
```python
from infra.datascience_agent.agents.tools.base_tool import BaseTool, ToolInputSchema, ToolOutputSchema
from pydantic import Field

class CustomAnalysisInput(ToolInputSchema):
    """自定义分析工具输入模式"""
    data_source: str = Field(..., description="数据源名称")
    analysis_type: str = Field(..., description="分析类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="分析参数")

class CustomAnalysisTool(BaseTool):
    """自定义数据分析工具"""
    name: str = "custom_analysis"
    description: str = "执行自定义数据分析任务"
    args_schema = CustomAnalysisInput
    
    def _execute(self, data_source: str, analysis_type: str, 
                parameters: Dict[str, Any]) -> ToolOutputSchema:
        """
        执行自定义分析逻辑
        
        Args:
            data_source: 数据源名称
            analysis_type: 分析类型
            parameters: 分析参数
            
        Returns:
            ToolOutputSchema: 分析结果
        """
        try:
            # 实现具体的分析逻辑
            result = self._perform_analysis(data_source, analysis_type, parameters)
            
            return ToolOutputSchema(
                status="success",
                observation={
                    "analysis_result": result,
                    "data_source": data_source,
                    "analysis_type": analysis_type
                }
            )
        except Exception as e:
            return ToolOutputSchema(
                status="failure",
                observation={"error": str(e)},
                error_message=f"分析执行失败: {str(e)}"
            )
    
    def _perform_analysis(self, data_source: str, analysis_type: str, 
                         parameters: Dict[str, Any]) -> Dict[str, Any]:
        """实现具体的分析逻辑"""
        # 自定义分析实现
        pass
```

## 开发模式和调试

### 开发环境配置

#### 1. 调试模式启动
```python
import logging
import os

# 设置调试日志级别
logging.basicConfig(level=logging.DEBUG)
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["EXECUTION_MODE"] = "local"

# 启用详细追踪
from common.trace.trace import enable_debug_tracing
enable_debug_tracing()
```

#### 2. 单元测试框架
```python
import pytest
from unittest.mock import AsyncMock, MagicMock
from infra.datascience_agent.agent_service import AgentService

@pytest.fixture
async def mock_agent_service():
    """创建模拟的代理服务"""
    mock_mcp_manager = MagicMock()
    agent_service = AgentService(mock_mcp_manager)
    return agent_service

@pytest.mark.asyncio
async def test_intent_recognition(mock_agent_service):
    """测试意图识别功能"""
    user_input = "我想分析用户行为数据"
    session_id = "test_session"
    
    events = []
    async for event in mock_agent_service.stream_chat(user_input, session_id):
        events.append(event)
    
    # 验证事件类型和内容
    assert len(events) > 0
    assert any(event.type == "MessageEvent" for event in events)
```

### 调试工具和技巧

#### 1. 状态检查工具
```python
def debug_session_state(agent_service: AgentService, session_id: str):
    """调试会话状态"""
    state = agent_service.session_states.get(session_id)
    if state:
        print(f"Session {session_id} State:")
        print(f"  Intent: {state.get('identified_intent_name')}")
        print(f"  Plan: {bool(state.get('current_plan'))}")
        print(f"  Error: {state.get('execution_error')}")
        print(f"  Language: {state.get('detected_language')}")
    else:
        print(f"Session {session_id} not found")

def debug_active_tasks(agent_service: AgentService):
    """调试活跃任务"""
    print(f"Active tasks: {list(agent_service.active_graph_tasks.keys())}")
    for session_id, task in agent_service.active_graph_tasks.items():
        print(f"  {session_id}: {'running' if not task.done() else 'completed'}")
```

#### 2. 日志分析脚本
```python
import re
from datetime import datetime

def analyze_execution_logs(log_file_path: str):
    """分析执行日志"""
    with open(log_file_path, 'r') as f:
        logs = f.readlines()
    
    # 提取关键信息
    sessions = {}
    for line in logs:
        if 'session_id' in line:
            match = re.search(r'session_id:(\w+)', line)
            if match:
                session_id = match.group(1)
                if session_id not in sessions:
                    sessions[session_id] = []
                sessions[session_id].append(line.strip())
    
    # 分析每个会话
    for session_id, session_logs in sessions.items():
        print(f"\nSession {session_id}:")
        print(f"  Total logs: {len(session_logs)}")
        
        # 查找错误
        errors = [log for log in session_logs if 'ERROR' in log]
        if errors:
            print(f"  Errors: {len(errors)}")
            for error in errors[:3]:  # 显示前3个错误
                print(f"    {error}")
```

## 扩展开发指南

### 添加新的意图类型

#### 1. 定义意图模式
```python
# 在 intent_schemas.py 中添加
class CustomIntentInfo(BaseModel):
    """自定义意图信息"""
    category: str = "custom_intent"
    domain: str = Field(..., description="业务域")
    complexity: str = Field(default="medium", description="复杂度")
    required_resources: List[str] = Field(default_factory=list, description="所需资源")
```

#### 2. 更新识别提示词
```python
# 在 prompt.py 中添加
CUSTOM_INTENT_EXAMPLES = """
## custom_intent 意图示例

**任务定义**：
自定义业务逻辑处理任务

**例子**：
* "执行自定义分析流程"
* "运行特定业务规则"
* "处理专业领域数据"
"""

# 更新主提示词模板
SYSTEM_PROMPT_TEMPLATE += CUSTOM_INTENT_EXAMPLES
```

#### 3. 添加规划模板
```python
# 在 planner_prompts.py 中添加
CUSTOM_INTENT_TEMPLATE = """
### custom_intent意图
正确的子任务划分和对应的工具组合示例如下：
```
"数据预处理"任务工具组合: custom_preprocessing + finish
"自定义分析"任务工具组合: custom_analysis + finish
"结果后处理"任务工具组合: custom_postprocessing + finish
```
"""
```

### 添加新工具

#### 1. 实现工具类
```python
from infra.datascience_agent.agents.tools.base_tool import BaseTool, ToolInputSchema, ToolOutputSchema
from pydantic import Field
from typing import Dict, Any, List

class DataVisualizationInput(ToolInputSchema):
    """数据可视化工具输入"""
    data_description: str = Field(..., description="数据描述")
    chart_type: str = Field(..., description="图表类型")
    styling_preferences: Dict[str, Any] = Field(default_factory=dict, description="样式偏好")

class DataVisualizationTool(BaseTool):
    """数据可视化工具"""
    name: str = "data_visualization"
    description: str = "生成数据可视化图表"
    args_schema = DataVisualizationInput
    
    def _execute(self, data_description: str, chart_type: str, 
                styling_preferences: Dict[str, Any]) -> ToolOutputSchema:
        """
        执行数据可视化
        
        Args:
            data_description: 数据描述
            chart_type: 图表类型 (bar, line, scatter, heatmap, etc.)
            styling_preferences: 样式偏好设置
            
        Returns:
            ToolOutputSchema: 可视化结果
        """
        try:
            # 生成可视化代码
            viz_code = self._generate_visualization_code(
                data_description, chart_type, styling_preferences
            )
            
            return ToolOutputSchema(
                status="success",
                observation={
                    "visualization_code": viz_code,
                    "chart_type": chart_type,
                    "estimated_execution_time": "2-5秒"
                }
            )
        except Exception as e:
            return ToolOutputSchema(
                status="failure",
                observation={"error": str(e)},
                error_message=f"可视化生成失败: {str(e)}"
            )
    
    def _generate_visualization_code(self, data_description: str, 
                                   chart_type: str, 
                                   styling_preferences: Dict[str, Any]) -> str:
        """生成可视化代码"""
        base_template = {
            "bar": "plt.bar(x, y)",
            "line": "plt.plot(x, y)",
            "scatter": "plt.scatter(x, y)",
            "heatmap": "sns.heatmap(data)"
        }
        
        code_template = f"""
import matplotlib.pyplot as plt
import seaborn as sns

# 数据准备
# {data_description}

# 创建图表
plt.figure(figsize=(10, 6))
{base_template.get(chart_type, "plt.plot(x, y)")}

# 样式设置
plt.title('{styling_preferences.get("title", "数据分析图表")}')
plt.xlabel('{styling_preferences.get("xlabel", "X轴")}')
plt.ylabel('{styling_preferences.get("ylabel", "Y轴")}')

# 显示图表
plt.tight_layout()
plt.show()
"""
        return code_template.strip()
```

#### 2. 注册工具
```python
# 在 __init__.py 中注册新工具
from .data_visualization_tool import DataVisualizationTool

AVAILABLE_TOOLS = {
    "data_visualization": DataVisualizationTool,
    # ... 其他工具
}
```

#### 3. 更新工具组合
```python
# 在 planner_prompts.py 中添加工具组合
"""
"数据可视化"任务工具组合: data_visualization + jupyter__execute_code + finish
"""
```

### 自定义执行流程

#### 1. 添加新的图节点
```python
async def custom_processing_node(state: AgentState, 
                               custom_processor: Any) -> AgentState:
    """
    自定义处理节点
    
    Args:
        state: 当前状态
        custom_processor: 自定义处理器
        
    Returns:
        AgentState: 更新后的状态
    """
    logger.info("--- Node: Custom Processing ---")
    
    try:
        # 执行自定义处理逻辑
        result = await custom_processor.process(state)
        
        # 更新状态
        state['custom_processing_result'] = result
        state['custom_processing_completed'] = True
        
    except Exception as e:
        logger.error(f"Custom processing failed: {e}")
        state['execution_error'] = f"自定义处理失败: {str(e)}"
    
    return state
```

#### 2. 修改图结构
```python
def build_custom_graph(llm_client: 'OpenAIClient', 
                      executor_agent: 'ExecutorAgent',
                      custom_processor: Any):
    """构建包含自定义节点的图"""
    workflow = StateGraph(AgentState)
    
    # 添加标准节点
    workflow.add_node("userInput", get_user_input_node)
    workflow.add_node("intentRecognizer", partial(intent_recognition_node, llm_client=llm_client))
    workflow.add_node("customProcessor", partial(custom_processing_node, custom_processor=custom_processor))
    workflow.add_node("planner", partial(planner_node, llm_client=llm_client))
    workflow.add_node("executor", partial(executor_node, llm_client=llm_client))
    workflow.add_node("presentOutput", present_output_node)
    
    # 设置流程
    workflow.set_entry_point("userInput")
    workflow.add_edge("userInput", "intentRecognizer")
    workflow.add_edge("intentRecognizer", "customProcessor")
    workflow.add_edge("customProcessor", "planner")
    workflow.add_edge("planner", "executor")
    workflow.add_edge("executor", "presentOutput")
    workflow.add_edge("presentOutput", END)
    
    return workflow.compile()
```

## 性能优化指南

### 1. 异步编程最佳实践

#### 正确的异步模式
```python
# ✅ 正确：使用异步方法
async def process_multiple_queries(queries: List[str]):
    tasks = []
    for query in queries:
        task = asyncio.create_task(process_single_query(query))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results

# ❌ 错误：在异步函数中使用同步调用
async def bad_example(queries: List[str]):
    results = []
    for query in queries:
        result = process_single_query_sync(query)  # 阻塞调用
        results.append(result)
    return results
```

#### 资源管理
```python
# ✅ 正确：使用上下文管理器
async def with_database_connection():
    async with get_database_connection() as conn:
        # 使用连接
        result = await conn.execute("SELECT * FROM table")
        return result
    # 连接自动关闭

# ✅ 正确：使用信号量控制并发
semaphore = asyncio.Semaphore(10)  # 最多10个并发任务

async def controlled_execution(task):
    async with semaphore:
        return await execute_task(task)
```

### 2. 内存优化

#### 流式处理
```python
async def stream_large_dataset(dataset_path: str):
    """流式处理大数据集"""
    async with aiofiles.open(dataset_path, 'r') as f:
        async for line in f:
            # 逐行处理，避免加载整个文件到内存
            yield process_line(line)

# 使用示例
async for processed_item in stream_large_dataset("large_file.csv"):
    # 实时处理每个项目
    handle_processed_item(processed_item)
```

#### 缓存策略
```python
from functools import lru_cache
import asyncio

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._cache = {}
        self._cache_lock = asyncio.Lock()
    
    async def get_or_compute(self, key: str, compute_func, ttl: int = 3600):
        """获取缓存或计算新值"""
        async with self._cache_lock:
            if key in self._cache:
                value, timestamp = self._cache[key]
                if time.time() - timestamp < ttl:
                    return value
            
            # 计算新值
            value = await compute_func()
            self._cache[key] = (value, time.time())
            return value
```

### 3. 错误处理和重试机制

#### 指数退避重试
```python
import asyncio
import random

async def retry_with_exponential_backoff(func, max_retries: int = 3, 
                                       base_delay: float = 1.0):
    """
    使用指数退避的重试机制
    
    Args:
        func: 要重试的异步函数
        max_retries: 最大重试次数
        base_delay: 基础延迟时间
        
    Returns:
        函数执行结果
    """
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries:
                raise e
            
            # 计算延迟时间：base_delay * 2^attempt + 随机抖动
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay:.2f}s")
            await asyncio.sleep(delay)

# 使用示例
result = await retry_with_exponential_backoff(
    lambda: call_external_api(),
    max_retries=3,
    base_delay=1.0
)
```

#### 电路熔断器模式
```python
import time
from enum import Enum

class CircuitState(Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

class CircuitBreaker:
    """电路熔断器"""
    
    def __init__(self, failure_threshold: int = 5, 
                 recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    async def call(self, func):
        """通过熔断器调用函数"""
        if self.state == CircuitState.OPEN:
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func()
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _on_success(self):
        """成功时重置计数器"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        """失败时增加计数器"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
```

## 监控和可观测性

### 1. 指标收集

#### 自定义指标
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义指标
REQUEST_COUNT = Counter('agent_requests_total', 'Total requests', ['session_id', 'intent_type'])
REQUEST_DURATION = Histogram('agent_request_duration_seconds', 'Request duration')
ACTIVE_SESSIONS = Gauge('agent_active_sessions', 'Number of active sessions')

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.start_times = {}
    
    def start_request(self, session_id: str, intent_type: str):
        """开始请求计时"""
        self.start_times[session_id] = time.time()
        REQUEST_COUNT.labels(session_id=session_id, intent_type=intent_type).inc()
        ACTIVE_SESSIONS.inc()
    
    def end_request(self, session_id: str):
        """结束请求计时"""
        if session_id in self.start_times:
            duration = time.time() - self.start_times[session_id]
            REQUEST_DURATION.observe(duration)
            del self.start_times[session_id]
            ACTIVE_SESSIONS.dec()
```

#### 链路追踪
```python
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

def setup_tracing():
    """设置链路追踪"""
    trace.set_tracer_provider(TracerProvider())
    tracer = trace.get_tracer(__name__)
    
    jaeger_exporter = JaegerExporter(
        agent_host_name="localhost",
        agent_port=14268,
    )
    
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    return tracer

# 使用示例
tracer = setup_tracing()

async def traced_function():
    with tracer.start_as_current_span("custom_operation") as span:
        span.set_attribute("operation.type", "data_processing")
        # 执行业务逻辑
        result = await process_data()
        span.set_attribute("operation.result_size", len(result))
        return result
```

### 2. 日志最佳实践

#### 结构化日志
```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # 设置JSON格式化器
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_event(self, event_type: str, session_id: str, 
                  data: Dict[str, Any], level: str = "INFO"):
        """记录结构化事件"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "session_id": session_id,
            "level": level,
            "data": data
        }
        
        log_message = json.dumps(log_entry, ensure_ascii=False)
        getattr(self.logger, level.lower())(log_message)

# 使用示例
logger = StructuredLogger("agent.execution")

logger.log_event(
    event_type="task_started",
    session_id="session_123",
    data={
        "task_type": "data_science",
        "subtask_count": 5,
        "estimated_duration": "5-10分钟"
    }
)
```

## 测试策略

### 1. 单元测试

#### 测试意图识别
```python
@pytest.mark.asyncio
async def test_intent_recognition_data_science():
    """测试数据科学意图识别"""
    mock_llm_client = AsyncMock()
    
    # 模拟LLM响应
    mock_response = MagicMock()
    mock_response.content = '''
    {"task_category": "data_science", "layer": 3, "task_description": "销售数据趋势分析"}
    ===THINK===
    用户想要进行销售数据分析...
    ===MESSAGE===
    我将为您分析销售数据趋势。
    '''
    
    mock_llm_client.generate_stream.return_value = async_mock_stream(mock_response.content)
    
    recognizer = IntentRecognizer(mock_llm_client)
    
    events = []
    async for event in recognizer.process_query("分析销售数据趋势"):
        events.append(event)
    
    assert recognizer.get_task_category() == "data_science"
    assert recognizer.is_intent_recognition_complete()
```

#### 测试任务规划
```python
@pytest.mark.asyncio
async def test_plan_creation():
    """测试计划创建"""
    mock_llm_client = AsyncMock()
    
    # 模拟规划响应
    mock_plan_response = {
        "task_name": "销售数据分析",
        "subtasks": [
            {
                "idx": 1,
                "dep": [],
                "desc": "数据加载",
                "adv_tool": "generate_sql + jupyter__load_data_by_sql + finish"
            },
            {
                "idx": 2,
                "dep": [1],
                "desc": "趋势分析",
                "adv_tool": "nl2code + jupyter__execute_code + finish"
            }
        ]
    }
    
    mock_llm_client.generate.return_value.content = json.dumps(mock_plan_response)
    
    plan = await create_plan(
        llm_client=mock_llm_client,
        intent_name="data_science",
        intent_entities={},
        user_query="分析销售数据趋势"
    )
    
    assert plan.task_name == "销售数据分析"
    assert len(plan.subtasks) == 2
    assert plan.subtasks[0].desc == "数据加载"
```

### 2. 集成测试

#### 端到端测试
```python
@pytest.mark.asyncio
async def test_end_to_end_workflow():
    """端到端工作流测试"""
    # 设置测试环境
    test_mcp_manager = create_test_mcp_manager()
    agent_service = AgentService(test_mcp_manager)
    
    # 执行完整流程
    session_id = "test_e2e_001"
    user_input = "请分析用户行为数据并生成报告"
    
    events = []
    async for event in agent_service.stream_chat(user_input, session_id):
        events.append(event)
    
    # 验证执行流程
    event_types = [event.type for event in events]
    
    assert "MessageEvent" in event_types
    assert "ThinkEvent" in event_types
    assert "TaskListEvent" in event_types
    assert "JupyterEvent" in event_types
    assert "FinalSummaryEvent" in event_types
    
    # 验证最终状态
    final_state = agent_service.session_states[session_id]
    assert final_state['identified_intent_name'] == "data_science"
    assert final_state['current_plan'] is not None
```

### 3. 性能测试

#### 负载测试
```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

async def load_test(concurrent_users: int = 10, 
                   requests_per_user: int = 5):
    """负载测试"""
    agent_service = AgentService(create_test_mcp_manager())
    
    async def simulate_user(user_id: int):
        """模拟单个用户"""
        session_id = f"load_test_user_{user_id}"
        
        for i in range(requests_per_user):
            start_time = time.time()
            
            events = []
            async for event in agent_service.stream_chat(
                f"测试查询 {i+1}", session_id
            ):
                events.append(event)
            
            duration = time.time() - start_time
            print(f"User {user_id}, Request {i+1}: {duration:.2f}s, Events: {len(events)}")
    
    # 并发执行用户模拟
    tasks = [simulate_user(i) for i in range(concurrent_users)]
    await asyncio.gather(*tasks)

# 运行负载测试
asyncio.run(load_test(concurrent_users=5, requests_per_user=3))
```

## 部署指南

### 1. Docker 部署

#### Dockerfile
```dockerfile
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY pyproject.toml .

# 安装Python依赖
RUN pip install uv
RUN uv pip install -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV EXECUTION_MODE=local
ENV LOG_LEVEL=INFO

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "infra.endpoint.main"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  intellix-ds-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - EXECUTION_MODE=local
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/intellix_ds
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
  
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=intellix_ds
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  redis_data:
  postgres_data:
```

### 2. Kubernetes 部署

#### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intellix-ds-agent
  labels:
    app: intellix-ds-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: intellix-ds-agent
  template:
    metadata:
      labels:
        app: intellix-ds-agent
    spec:
      containers:
      - name: agent
        image: intellix-ds-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: EXECUTION_MODE
          value: "ray"
        - name: LOG_LEVEL
          value: "INFO"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: url
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

---

*开发者指南版本: v1.0*  
*最后更新: 2025-08-14*  
*目标读者: 后端开发工程师、DevOps工程师*

# Intellix Data Science Agent 架构设计文档

## 系统架构概览

Intellix Data Science Agent 采用分层架构设计，基于 LangGraph 状态图实现智能任务编排，通过多个专业化代理协同工作，完成复杂的数据科学任务。

## 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        WebUI[Web界面]
        API_Client[API客户端]
        CLI[命令行工具]
    end
    
    subgraph "网关层"
        Gateway[API网关]
        Auth[认证授权]
        RateLimit[限流控制]
    end
    
    subgraph "应用服务层"
        AgentService[AgentService<br/>主服务入口]
        SessionMgr[会话管理器]
        EventStream[事件流处理器]
    end
    
    subgraph "编排层"
        GraphOrchestrator[GraphOrchestrator<br/>流程编排器]
        StateManager[状态管理器]
        Router[路由决策器]
    end
    
    subgraph "智能代理层"
        IntentAgent[IntentRecognizer<br/>意图识别代理]
        PlannerAgent[Planner<br/>任务规划代理]
        ExecutorAgent[ExecutorAgent<br/>任务执行代理]
    end
    
    subgraph "工具执行层"
        FlexibleReact[FlexibleReactAgent<br/>灵活反应代理]
        ToolRegistry[工具注册表]
        MCPManager[MCP管理器]
    end
    
    subgraph "工具层"
        SQLGen[SQL生成]
        CodeGen[代码生成]
        JupyterExec[Jupyter执行]
        SemanticSearch[语义检索]
        DataLake[数据湖计算]
    end
    
    subgraph "数据层"
        PostgreSQL[(PostgreSQL)]
        Redis[(Redis缓存)]
        DataLakeStorage[(数据湖存储)]
        KnowledgeBase[(知识库)]
    end
    
    subgraph "基础设施层"
        Monitoring[监控系统]
        Logging[日志系统]
        Tracing[链路追踪]
    end
    
    WebUI --> Gateway
    API_Client --> Gateway
    CLI --> Gateway
    
    Gateway --> Auth
    Auth --> RateLimit
    RateLimit --> AgentService
    
    AgentService --> SessionMgr
    AgentService --> EventStream
    AgentService --> GraphOrchestrator
    
    GraphOrchestrator --> StateManager
    GraphOrchestrator --> Router
    GraphOrchestrator --> IntentAgent
    GraphOrchestrator --> PlannerAgent
    GraphOrchestrator --> ExecutorAgent
    
    ExecutorAgent --> FlexibleReact
    FlexibleReact --> ToolRegistry
    FlexibleReact --> MCPManager
    
    MCPManager --> SQLGen
    MCPManager --> CodeGen
    MCPManager --> JupyterExec
    MCPManager --> SemanticSearch
    MCPManager --> DataLake
    
    SQLGen --> PostgreSQL
    CodeGen --> JupyterExec
    JupyterExec --> Redis
    SemanticSearch --> KnowledgeBase
    DataLake --> DataLakeStorage
    
    AgentService --> Monitoring
    AgentService --> Logging
    AgentService --> Tracing
```

## 核心组件详细设计

### 1. AgentService 设计

```mermaid
classDiagram
    class AgentService {
        -llm_client: OpenAIClient
        -mcp_manager: MCPManager
        -data_loader: DataLoader
        -executor_agent: ExecutorAgent
        -app: StateGraph
        -session_states: Dict[str, AgentState]
        -active_graph_tasks: Dict[str, asyncio.Task]
        -initial_graph_state_template: AgentState
        
        +__init__(mcp_manager: MCPManager)
        +stream_chat(user_input: str, session_id: str, ...) AsyncIterator[AgentEvent]
        +request_stop(session_id: str) bool
        +_get_session_state(session_id: str) AgentState
        +_save_session_state(session_id: str, state: AgentState)
        +_get_initial_state_template() AgentState
    }
    
    class OpenAIClient {
        -client: AsyncOpenAI
        -default_model: str
        -temperature: float
        
        +generate(messages: List, tools: List, ...) ChatCompletionMessage
        +generate_stream(messages: List, ...) AsyncIterator[ChatCompletionChunk]
    }
    
    class MCPManager {
        -mcp_servers: Dict
        -tool_registry: Dict
        
        +call_tool(tool_name: str, arguments: Dict) Any
        +get_available_tools() List[str]
        +register_server(name: str, config: Dict)
    }
    
    AgentService --> OpenAIClient
    AgentService --> MCPManager
    AgentService --> DataLoader
    AgentService --> ExecutorAgent
```

**设计原则**:
- **单一职责**: 每个组件专注于特定功能
- **依赖注入**: 通过构造函数注入依赖
- **状态隔离**: 每个会话维护独立状态
- **异步优先**: 所有I/O操作使用异步模式

### 2. 意图识别系统设计

```mermaid
stateDiagram-v2
    [*] --> Layer1_Init: 用户输入
    
    state Layer1_Init {
        [*] --> 语言检测
        语言检测 --> 权限验证
        权限验证 --> 基础意图识别
        基础意图识别 --> 必要信息提取
        必要信息提取 --> 完整性检查
    }
    
    state Layer1_Loop {
        [*] --> 询问补充信息
        询问补充信息 --> 等待用户回复
        等待用户回复 --> 信息整合
        信息整合 --> 完整性检查
        完整性检查 --> [*]: 信息充足
        完整性检查 --> 询问补充信息: 信息不足且轮次<3
    }
    
    state Layer2 {
        [*] --> 收集补充信息
        收集补充信息 --> 参数优化
        参数优化 --> [*]
    }
    
    state Layer3 {
        [*] --> 最终确认
        最终确认 --> 生成完整描述
        生成完整描述 --> [*]
    }
    
    Layer1_Init --> Layer1_Loop: 信息不足
    Layer1_Init --> Layer2: 信息充足但需补充
    Layer1_Init --> Layer3: 信息完全充足
    Layer1_Loop --> Layer2: 基础信息收集完成
    Layer1_Loop --> Layer3: 轮次达到上限
    Layer2 --> Layer3: 补充信息收集完成
    Layer3 --> [*]: 意图识别完成
```

### 3. 任务规划系统设计

```mermaid
flowchart TD
    Input[意图和实体] --> Analyze[分析任务类型]
    Analyze --> Template{选择模板}
    
    Template -->|数据科学| DSTemplate[数据科学模板]
    Template -->|数据库查询| DBTemplate[数据库查询模板]
    Template -->|文档查询| DocTemplate[文档查询模板]
    Template -->|投诉举报| ComplaintTemplate[投诉举报模板]
    
    DSTemplate --> DSPlan[生成数据科学计划]
    DBTemplate --> DBPlan[生成数据库查询计划]
    DocTemplate --> DocPlan[生成文档查询计划]
    ComplaintTemplate --> ComplaintPlan[生成投诉举报计划]
    
    DSPlan --> Validate[验证计划]
    DBPlan --> Validate
    DocPlan --> Validate
    ComplaintPlan --> Validate
    
    Validate --> Valid{计划有效?}
    Valid -->|是| Output[输出计划]
    Valid -->|否| Fallback[生成备用计划]
    
    Fallback --> Output
    Output --> End[完成规划]
```

### 4. 执行系统设计

```mermaid
classDiagram
    class ExecutorAgent {
        -llm_client: OpenAIClient
        -mcp_manager: MCPManager
        -writer: StreamWriter
        -react_agent: FlexibleReactAgent
        
        +initialize() async
        +execute_subtasks(plan: Dict, state: AgentState) AsyncGenerator
    }
    
    class FlexibleReactAgent {
        -llm_client: OpenAIClient
        -mcp_manager: MCPManager
        -conversation_history: List[Dict]
        -subtask_status: Dict[int, str]
        -overall_task_name: str
        
        +initialize() async
        +process_subtask(subtask: Dict, state: AgentState) AsyncGenerator
        +create_enhanced_system_prompt(language: str, state: Dict) str
        +get_env_params_from_mcp_manager() Dict
    }
    
    class ToolRegistry {
        -available_tools: Dict[str, BaseTool]
        
        +register_tool(name: str, tool: BaseTool)
        +get_tool(name: str) BaseTool
        +list_tools() List[str]
    }
    
    ExecutorAgent --> FlexibleReactAgent
    FlexibleReactAgent --> ToolRegistry
    FlexibleReactAgent --> MCPManager
```

## 数据流设计

### 状态传递流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant AS as AgentService
    participant State as 状态管理
    participant IR as IntentRecognizer
    participant PL as Planner
    participant EX as ExecutorAgent
    
    User->>AS: 用户输入
    AS->>State: 获取/创建会话状态
    State-->>AS: 返回当前状态
    
    AS->>IR: 传递输入和状态
    IR->>IR: 更新意图识别状态
    IR-->>AS: 返回更新后状态
    AS->>State: 保存状态更新
    
    AS->>PL: 传递完整意图信息
    PL->>PL: 生成执行计划
    PL-->>AS: 返回计划
    AS->>State: 保存计划到状态
    
    AS->>EX: 传递计划和完整状态
    EX->>EX: 执行子任务
    EX-->>AS: 流式返回执行事件
    AS-->>User: 转发事件流
```

### 事件驱动架构

```mermaid
graph LR
    subgraph "事件生产者"
        IR_Events[意图识别事件]
        PL_Events[规划事件]
        EX_Events[执行事件]
        Tool_Events[工具事件]
    end
    
    subgraph "事件总线"
        EventBus[事件总线]
        EventQueue[事件队列]
    end
    
    subgraph "事件消费者"
        UI_Handler[UI事件处理器]
        Log_Handler[日志事件处理器]
        Metric_Handler[指标事件处理器]
        Notification[通知处理器]
    end
    
    IR_Events --> EventBus
    PL_Events --> EventBus
    EX_Events --> EventBus
    Tool_Events --> EventBus
    
    EventBus --> EventQueue
    EventQueue --> UI_Handler
    EventQueue --> Log_Handler
    EventQueue --> Metric_Handler
    EventQueue --> Notification
```

## 工具系统架构

### 工具分类和层次

```mermaid
graph TD
    subgraph "工具抽象层"
        BaseTool[BaseTool<br/>工具基类]
        ToolSchema[工具模式定义]
    end
    
    subgraph "核心工具层"
        SQLTool[SQL生成工具]
        CodeTool[代码生成工具]
        ExecTool[执行工具]
        SearchTool[检索工具]
    end
    
    subgraph "专业工具层"
        MLTool[机器学习工具]
        VisTool[可视化工具]
        DataTool[数据处理工具]
        AnalyticsTool[分析工具]
    end
    
    subgraph "外部服务层"
        JupyterKernel[Jupyter内核]
        DatabaseService[数据库服务]
        LLMService[LLM服务]
        StorageService[存储服务]
    end
    
    BaseTool --> SQLTool
    BaseTool --> CodeTool
    BaseTool --> ExecTool
    BaseTool --> SearchTool
    
    SQLTool --> MLTool
    CodeTool --> VisTool
    ExecTool --> DataTool
    SearchTool --> AnalyticsTool
    
    MLTool --> JupyterKernel
    VisTool --> JupyterKernel
    DataTool --> DatabaseService
    AnalyticsTool --> LLMService
```

### MCP (Model Context Protocol) 集成

```mermaid
sequenceDiagram
    participant Agent as 执行代理
    participant MCP as MCP管理器
    participant Server as MCP服务器
    participant Tool as 具体工具
    
    Agent->>MCP: 请求工具调用
    MCP->>MCP: 解析工具名称
    MCP->>Server: 转发请求到对应服务器
    Server->>Tool: 调用具体工具实现
    Tool-->>Server: 返回执行结果
    Server-->>MCP: 返回结果
    MCP->>MCP: 格式化响应
    MCP-->>Agent: 返回标准化结果
```

## 数据管理架构

### 数据加载器设计

```mermaid
classDiagram
    class DataLoader {
        -mcp_manager: MCPManager
        
        +get_database_schema_async(dataset_id: str) Dict[str, Any]
        +sample_table_data_async(table_name: str, ...) Dict[str, Any]
        +sample_multiple_tables(...) Dict[str, Dict[str, Any]]
        +get_table_preview(table_name: str, ...) str
        -_get_dlc_instance_name() str
        -_parse_dlc_list_tables_result(result: Any) List
        -_get_known_table_names() List[str]
    }
    
    class MCPManager {
        +call_tool(tool_name: str, arguments: Dict) Any
        +get_server_status() Dict
    }
    
    DataLoader --> MCPManager
```

### 数据流转模式

```mermaid
flowchart LR
    subgraph "数据源"
        DB1[(业务数据库)]
        DB2[(数据仓库)]
        Lake[(数据湖)]
        Files[(文件存储)]
    end
    
    subgraph "数据加载层"
        Loader[DataLoader]
        Schema[Schema管理器]
        Sampler[数据采样器]
    end
    
    subgraph "数据处理层"
        Transform[数据转换]
        Clean[数据清洗]
        Feature[特征工程]
    end
    
    subgraph "数据消费层"
        Analysis[数据分析]
        ML[机器学习]
        Viz[数据可视化]
    end
    
    DB1 --> Loader
    DB2 --> Loader
    Lake --> Loader
    Files --> Loader
    
    Loader --> Schema
    Loader --> Sampler
    
    Schema --> Transform
    Sampler --> Clean
    Transform --> Feature
    Clean --> Feature
    
    Feature --> Analysis
    Feature --> ML
    Feature --> Viz
```

## 状态管理设计

### 状态生命周期

```mermaid
stateDiagram-v2
    [*] --> Created: 创建会话
    
    state Created {
        [*] --> 初始化状态
        初始化状态 --> 设置默认值
        设置默认值 --> [*]
    }
    
    Created --> Active: 开始处理
    
    state Active {
        [*] --> 意图识别中
        意图识别中 --> 任务规划中
        任务规划中 --> 任务执行中
        任务执行中 --> 结果生成中
        结果生成中 --> [*]
    }
    
    Active --> Paused: 暂停请求
    Paused --> Active: 恢复执行
    Active --> Stopped: 停止请求
    Active --> Completed: 任务完成
    Active --> Error: 执行错误
    
    Error --> Active: 错误恢复
    Stopped --> [*]: 清理资源
    Completed --> [*]: 清理资源
```

### 状态持久化策略

```mermaid
graph TB
    subgraph "内存状态"
        SessionState[会话状态]
        TaskState[任务状态]
        CacheState[缓存状态]
    end
    
    subgraph "持久化层"
        Redis[(Redis)]
        Database[(数据库)]
        FileSystem[(文件系统)]
    end
    
    subgraph "恢复机制"
        StateRecovery[状态恢复]
        Checkpoint[检查点]
        Rollback[回滚机制]
    end
    
    SessionState --> Redis
    TaskState --> Database
    CacheState --> FileSystem
    
    Redis --> StateRecovery
    Database --> Checkpoint
    FileSystem --> Rollback
```

## 并发和性能设计

### 并发执行模型

```mermaid
graph TB
    subgraph "请求处理层"
        Request1[请求1]
        Request2[请求2]
        RequestN[请求N]
    end
    
    subgraph "会话管理层"
        SessionPool[会话池]
        LoadBalancer[负载均衡器]
    end
    
    subgraph "执行层"
        Worker1[工作线程1]
        Worker2[工作线程2]
        WorkerN[工作线程N]
    end
    
    subgraph "资源层"
        JupyterPool[Jupyter内核池]
        DBPool[数据库连接池]
        LLMPool[LLM连接池]
    end
    
    Request1 --> LoadBalancer
    Request2 --> LoadBalancer
    RequestN --> LoadBalancer
    
    LoadBalancer --> SessionPool
    SessionPool --> Worker1
    SessionPool --> Worker2
    SessionPool --> WorkerN
    
    Worker1 --> JupyterPool
    Worker2 --> DBPool
    WorkerN --> LLMPool
```

### 性能优化策略

1. **异步I/O**: 所有网络和文件操作使用异步模式
2. **连接池**: 复用数据库和服务连接
3. **缓存机制**: 多层缓存减少重复计算
4. **批量处理**: 合并相似的工具调用
5. **资源预分配**: 预先分配常用资源

## 安全架构设计

### 安全层次模型

```mermaid
graph TB
    subgraph "网络安全层"
        Firewall[防火墙]
        TLS[TLS加密]
        VPN[VPN接入]
    end
    
    subgraph "应用安全层"
        Authentication[身份认证]
        Authorization[权限授权]
        InputValidation[输入验证]
    end
    
    subgraph "数据安全层"
        Encryption[数据加密]
        Masking[数据脱敏]
        Audit[审计日志]
    end
    
    subgraph "执行安全层"
        Sandbox[代码沙箱]
        ResourceLimit[资源限制]
        TimeLimit[时间限制]
    end
    
    User[用户] --> Firewall
    Firewall --> TLS
    TLS --> Authentication
    Authentication --> Authorization
    Authorization --> InputValidation
    InputValidation --> Sandbox
    Sandbox --> ResourceLimit
    ResourceLimit --> Encryption
    Encryption --> Audit
```

### 权限控制模型

```mermaid
erDiagram
    User ||--o{ Role : has
    Role ||--o{ Permission : contains
    Permission ||--o{ Resource : controls
    Resource ||--o{ Operation : allows
    
    User {
        string user_id
        string username
        string email
    }
    
    Role {
        string role_id
        string role_name
        string description
    }
    
    Permission {
        string permission_id
        string permission_name
        string resource_type
    }
    
    Resource {
        string resource_id
        string resource_name
        string resource_type
    }
    
    Operation {
        string operation_id
        string operation_name
        string operation_type
    }
```

## 监控和可观测性

### 监控架构

```mermaid
graph TB
    subgraph "应用层监控"
        AppMetrics[应用指标]
        BusinessMetrics[业务指标]
        UserMetrics[用户指标]
    end
    
    subgraph "基础设施监控"
        SystemMetrics[系统指标]
        NetworkMetrics[网络指标]
        StorageMetrics[存储指标]
    end
    
    subgraph "监控收集"
        Prometheus[Prometheus]
        Grafana[Grafana]
        AlertManager[告警管理]
    end
    
    subgraph "日志系统"
        LogCollector[日志收集器]
        LogStorage[日志存储]
        LogAnalysis[日志分析]
    end
    
    subgraph "链路追踪"
        Jaeger[Jaeger]
        OpenTelemetry[OpenTelemetry]
    end
    
    AppMetrics --> Prometheus
    BusinessMetrics --> Prometheus
    UserMetrics --> Prometheus
    SystemMetrics --> Prometheus
    NetworkMetrics --> Prometheus
    StorageMetrics --> Prometheus
    
    Prometheus --> Grafana
    Prometheus --> AlertManager
    
    AgentService --> LogCollector
    LogCollector --> LogStorage
    LogStorage --> LogAnalysis
    
    AgentService --> OpenTelemetry
    OpenTelemetry --> Jaeger
```

### 关键指标定义

1. **性能指标**
   - 响应时间: P50, P95, P99
   - 吞吐量: QPS, TPS
   - 并发数: 活跃会话数

2. **业务指标**
   - 任务成功率
   - 意图识别准确率
   - 工具调用成功率

3. **资源指标**
   - CPU使用率
   - 内存使用率
   - 网络I/O
   - 磁盘I/O

## 部署架构

### 微服务部署模式

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
        Gateway[API网关]
    end
    
    subgraph "应用服务层"
        Agent1[Agent实例1]
        Agent2[Agent实例2]
        AgentN[Agent实例N]
    end
    
    subgraph "中间件层"
        Redis[(Redis集群)]
        MQ[消息队列]
        Config[配置中心]
    end
    
    subgraph "数据层"
        PG_Master[(PostgreSQL主)]
        PG_Slave[(PostgreSQL从)]
        DataLake[(数据湖)]
    end
    
    subgraph "外部服务"
        LLM_API[LLM API]
        Jupyter_Service[Jupyter服务]
        MCP_Services[MCP服务集群]
    end
    
    LB --> Gateway
    Gateway --> Agent1
    Gateway --> Agent2
    Gateway --> AgentN
    
    Agent1 --> Redis
    Agent2 --> MQ
    AgentN --> Config
    
    Agent1 --> PG_Master
    Agent2 --> PG_Slave
    AgentN --> DataLake
    
    Agent1 --> LLM_API
    Agent2 --> Jupyter_Service
    AgentN --> MCP_Services
```

---

*架构设计文档版本: v1.0*  
*设计团队: Intellix Architecture Team*  
*审核状态: 已审核*

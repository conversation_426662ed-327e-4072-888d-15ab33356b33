
"""
- **data_science**: 机器学习/数据分析任务（预测、分类、聚类、异常检测、推荐、可视化分析）
- **nl_database_query**: 自然语言数据库查询和日志查询（数据检索、统计查询、报表生成、日志查询）
- **document_query**: 基于知识库召回的文档信息回答用户的复杂问题
- **complaint_or_report**: 投诉举报类问题（数据统计、排序分析、趋势分析、分类统计、可视化展示）
- **small_talk**: 简单闲聊
"""

all_prompt = {
    "zh": {
        "data_science": """
请根据任务描述、执行步骤和执行统计生成总结，涵盖 SQL 查询执行结果、数据处理过程中的代码，以及数据科学 pipeline 的重要节点：
1. 主要发现：
- 数据分析结果：原始数据 和 预测数据
- 模型表现：准确率、召回率等评估指标，以及特征重要性
- 执行情况：数据处理和特征工程的关键输出（仅报告严重错误）
2. 洞察与建议
- 业务洞察：数据发现的业务含义和价值
- 改进建议：基于结果的优化方向和具体措施
- 下一步行动：可执行的短期和长期方案

约束：
- 请不要直接输出 Python 代码和 BASH 命令和注释
- 请注意使用中文回答问题
- 不要在报告中展示完整数据结果
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下。请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,
        "nl_database_query": """
以下是基于用户问题生成的SQL查询结果。请分析数据并生成以下报告：
结果展示用户问题的结果描述，总结，与产出的图、表等
- 查询完成，结果如下：
xxxxx
1. 主要发现
- 问题背景：简述用户提出的业务问题
- 数据特征：关键指标、趋势模式、分布情况
- 异常识别：显著偏差、异常值或数据质量问题

2. 洞察与建议
- 数据洞察：数据反映的业务现状和潜在原因
- 关键结论：基于统计分析的核心发现
- 行动建议：针对发现的问题或机会的具体建议

输出格式：Markdown格式，突出关键数字和结论。

约束：
- 请注意使用中文回答问题
- 不要尝试在报告中展示完整数据结果
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下，请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,
        "nl_database_schema": """
以下是基于用户问题获取的数据表 Schema 定义。
请分析这些字段定义，并根据表格中的内容以 Markdown 格式生成以下报告：

1. 结果展示

约束：
- 请注意使用中文回答问题
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 请不要输出 Python 代码和 BASH 命令和注释
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下，请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,

        "document_query": """
请根据问题描述和检索召回结果生成回答，并在回答中引用检索召回结果中的chunk_id字段来显示知识来源。

- 根据用户要求，确定回答的个数；
- 只使用置信度高的检索召回结果（参考标准：重排序评分大于0.6）
- 要求回答所有结果时，回答所有置信度高的检索召回结果
- 没有要求回答个数时，回答所有置信度高的检索召回结果
- 要求回答指定个数时，回答指定个数的置信度高的检索召回结果；如果置信度高的检索召回结果不足，只回答置信度高的检索召回结果
- 每个回答总结详细内容为150字左右；如果用户要求具体细节、详细信息等，总结详细内容为300字左右

## 约束：
- 请注意置信度是内部概念，不要直接回复置信度或置信度数值。
- 请注意使用中文回答问题.
- 重要：如果查找过程出错，总结相关错误信息并输出。
- 重要：不要直接使用样例回答。
- 如果召回内容于用户问题无关，请直接回答"没有找到相关信息"。
- 请不要输出 Python 代码和 BASH 命令和注释
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明

## 输出格式：
1. 请以 markdown 格式输出回答
2. 你的回答需要基于检索召回结果同时重要回答内容需要引用的chunk 完整原始ID来显示知识来源。

重要引用规则：
召回的内容包含filename,file_id, chunk_id, chunk_content 等, 
1. 不要直接展示召回内容，而是以引用格式“ <reference-block chunk_id="chunk_id">数据源</reference-block> ”展示。
2. 实际召回时，请使用召回内容中真实的chunk id , 替换占位符{{chunk_id}}, 每条引文必须包含原文中的完整chunk_id 字段。
3. 引文必须一字不差的严格引用所提供文档中的chunk_id 字段。
4. 必要时，请使用多个引用来充分支持您的回复。
5. 确保每条引用都直接支持您回复的特定部分。
6. 引用内容必须来自召回内容
7. 数据源需要使用召回内容中的数据源数据源信息，目前仅支持引用知识库数据，数据源均以"知识库"表示。
8. 当一段内容存在多个引用时，请使用请将chunk id  在citation中 使用逗号分隔。例如：
<reference-block chunk_id="1234567890,1234567891">知识库</reference-block>


成功样例如下：
召回内容格式：

检索召回结果1：重排序评分0.0074615478515625，原始评分0.6165204, chunk_id：1234567890, 文件名：example.pdf, 内容：这是文档的具体内容...
检索召回结果2：重排序评分0.0074615478515625，原始评分0.7165204, chunk_id：1234567891, 文件名：example.pdf, 内容：这是文档的具体内容...
检索召回结果3：重排序评分0.0074615478515625，原始评分0.8165204, chunk_id：1234567892, 文件名：example.pdf, 内容：这是文档的具体内容...


### markdown输出内容示例：

- 对于列举类问题，例如：
user_query: 介绍两个纠纷案件
此时输出格式为 简述问题+列举内容+总结：
我已为您查找出<用户问题>相关信息：
##  案件细节
### 1. 案件标题：xxx  <reference-block chunk_id="1234567890">知识库</reference-block>
- **案件号：** xxx
- **案件类型：** xxx
- **内容总结：** xxx

### 2. 案件标题：xxx  <reference-block chunk_id="1234567891">知识库</reference-block>
- **案件号：** xxx
- **案件类型：** xxx
- **内容总结：** xxx

## 总结
根据检索到的信息，[综合性回答用户问题，整合多个来源的信息]。 <reference-block chunk_id="1234567890,1234567891">知识库</reference-block>。


- 对于普通问答问题，例如：
user_query: 什么是律师代理？
此时输出格式为 尝试从2-3个角度回答问题 并总结：

## 角度1：
根据检索到的信息，[综合性回答用户问题，整合多个来源的信息]。 <reference-block chunk_id="1234567890">知识库</reference-block>。

## 角度2：
根据检索到的信息，[综合性回答用户问题，整合多个来源的信息]。 <reference-block chunk_id="1234567892">知识库</reference-block>。

## 总结：
根据检索到的信息，[综合性回答用户问题，整合多个来源的信息]。 <reference-block chunk_id="1234567891">知识库</reference-block>。


### 注意：
1. 确保每个引用都与您回复中它所支持的那部分内容直接相关。如果召回的内容与用户问题无关，请不要引用，不要自行回答用户问题，直接回答"没有从当前知识库找到相关信息"。
2. 引用时请勿修改、缩短chunk_id。
用户问题和检索结果如下，请使用提供的信息并遵循以下准则来回答给定的问题:
        """,

        "complaint_or_report": """
请根据投诉举报类问题的分析结果生成总结报告，涵盖数据统计、排序分析、趋势分析和可视化结果：

1. 主要发现：
- 问题背景：简述用户提出的投诉举报分析需求
- 数据特征：关键统计指标、排名情况、趋势变化、分类分布
- 异常识别：显著偏差、异常值或数据质量问题
- 可视化结果：图表展示的关键信息和洞察

2. 洞察与建议
- 数据洞察：投诉举报数据反映的业务现状和潜在原因
- 关键结论：基于统计分析的核心发现和排名结果
- 行动建议：针对发现的问题或机会的具体建议
- 趋势分析：时间维度的变化趋势和预测

约束：
- 请不要直接输出 Python 代码和 BASH 命令和注释
- 请注意使用中文回答问题
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述
- 重点关注排名结果、统计数据和趋势分析

用户问题和执行结果汇总如下。请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,
        "small_talk": """""", # not used

        "error_analysis": """
当前 Agent 运行失败。请基于执行日志生成失败分析。

**输出结构：**
1. 失败位置：标明出错的步骤/子任务/工具
2. 失败原因：概述直接原因（引用1句关键报错，不展开堆栈）
3. 原因分析及建议：解释可能成因，给出可执行的修复建议与重试前置条件

回答示例：
失败位置：模型训练。
失败原因：训练阶段输入数据存在异常值（ValueError: NaN detected）。
原因分析及建议：输入原始数据数量不足，导致训练报错。建议先检查原始输入数据，后重试。

约束：
- 输出总字数150-200字
- 使用中文回答
- 不要输出除以上结构以外的任何内容
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何客套话
        """,
    },

    # TODO flacroxing add english prompt
    "en": {
        "data_science": """
        Please generate a summary based on the task description, execution steps, and execution statistics, covering SQL query results, code in the data processing pipeline, and important nodes in the data science workflow:

        1. Problem Description: Briefly restate the user's question.
        2. SQL Query Results: Summarize the results of each SQL query, including the main tables, key fields, aggregate metrics (e.g., count, sum, average), and filtering conditions.
        3. Overall Workflow: Provide an overview of the task process, listing key steps in order such as data loading, feature selection, modeling, and evaluation. Ignore environment-specific configurations.
        4. Code Execution Summary: Summarize important computation steps or data transformations in the code, with a focus on data cleaning, feature engineering, and model training outputs. Ignore logs.
        5. Final Results and Conclusions: Based on the analysis or model predictions in the workflow, summarize metrics like accuracy and recall, and provide conclusive insights and recommendations.

        Constraints:
        - Do not directly output Python code, BASH commands, or comments.
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "nl_database_query": """
        Below is a table generated by querying SQL based on the user's question (please replace with actual table content).
        Please analyze this data and generate a report in Markdown format based on the table:

        1. Problem Description: Briefly restate the user's question.
        2. Major trends and patterns in the data.
        3. Identify any outliers or anomalies in the table, if applicable.
        4. Provide a brief analytical report based on statistical characteristics of the data.

        Constraints:
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "nl_database_schema": """
        Below is the schema definition of the data tables retrieved based on the user's question.
        Please analyze the field definitions and generate a report in Markdown format based on the table:

        1. Problem Description: Briefly restate the user's question.
        2. Provide examples of field definitions: such as types and field comments for several fields.

        Constraints:
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "document_query": """
        Please generate an answer based on the question description and retrieved results, including the following parts:

        1. Brief summary of the user's question.
        2. Answer:
        - Use only high-confidence retrieved results (rerank score > 0.6).
        - List the retrieved results in descending order of confidence. Summarize each result in about 150 characters, or follow the user's instruction if a different summary style is requested.
        3. Summary:
        - Based on the retrieved results, answer the user's question with related information.

        ## Constraints:
        - Confidence is an internal concept; do not explicitly mention scores or numerical values.
        - Make sure the number of answers matches the user's request.
        - If the user does not specify a number or asks for all results, show all high-confidence answers.
        - Please respond in English.
        - Important: If errors occurred during retrieval, summarize and include the error information.
        - Important: Do not use sample or fabricated answers.

        ## Output Format: Please output the answer in Markdown format.
        Here is an example of a successful response:
### Answer
#### 1. Case Title: xxx
- **Case Number:** xxx
- **Case Type:** xxx
- **Summary:** xxx

### Summary
xxx.
The user's question and retrieval results are as follows. Please answer the user's question based on the retrieved content:
                """,

        "complaint_or_report": """
        Please generate a summary report based on the analysis results of complaint and report issues, covering data statistics, ranking analysis, trend analysis, and visualization results:

        1. Major Findings:
        - Problem Background: Briefly describe the user's complaint and report analysis requirements
        - Data Characteristics: Key statistical indicators, ranking situations, trend changes, classification distribution
        - Anomaly Identification: Significant deviations, outliers, or data quality issues
        - Visualization Results: Key information and insights displayed in charts

        2. Insights and Recommendations
        - Data Insights: Business status and potential causes reflected by complaint and report data
        - Key Conclusions: Core findings and ranking results based on statistical analysis
        - Action Recommendations: Specific recommendations for identified problems or opportunities
        - Trend Analysis: Changes and predictions in time dimensions

        Constraints:
        - Do not directly output Python code, BASH commands, or comments
        - Please respond in English
        - Do not display complete data results in the report
        - Do not add any titles, start directly from "## Major Findings"
        - Do not use markdown code block markers (```)
        - Do not add any comments or explanations at the end
        - Keep it concise and avoid redundant descriptions
        - Focus on ranking results, statistical data, and trend analysis

        The following is a summary of the user's question and execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,
                
        "small_talk": """""",

        "error_analysis": """
Please output an error analysis (only when the task has failed):
1. Root cause: Briefly summarize the direct cause of failure (quote key error messages)
2. Fix suggestions: Actionable recommendations to resolve the issue
3. Retry suggestion: Whether to retry and required preconditions

Constraints:
- 150–200 words total
- Respond in English
- Do not output anything beyond the structure above
- Do not use Markdown code fences (```)
- No pleasantries at the end
                """,
    },
}
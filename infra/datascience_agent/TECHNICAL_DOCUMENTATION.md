# Intellix Data Science Agent 技术文档

## 概述

Intellix Data Science Agent 是一个基于 LangGraph 的智能数据科学代理系统，采用多阶段流水线架构，能够理解用户的自然语言需求，自动生成执行计划，并通过工具调用完成复杂的数据科学任务。

## 系统架构

### 核心架构图

```mermaid
graph TB
    subgraph "用户交互层"
        UI[用户界面]
        API[API接口]
    end
    
    subgraph "服务层"
        AS[AgentService<br/>会话管理]
        GO[GraphOrchestrator<br/>流程编排]
    end
    
    subgraph "智能代理层"
        IR[IntentRecognizer<br/>意图识别]
        PL[Planner<br/>任务规划]
        EX[ExecutorAgent<br/>任务执行]
    end
    
    subgraph "工具层"
        SQL[SQL生成工具]
        CODE[代码生成工具]
        JUPYTER[Jupyter执行环境]
        SEARCH[语义检索工具]
        DLC[数据湖计算工具]
    end
    
    subgraph "数据层"
        DB[(数据库)]
        KB[(知识库)]
        LAKE[(数据湖)]
    end
    
    UI --> API
    API --> AS
    AS --> GO
    GO --> IR
    IR --> PL
    PL --> EX
    EX --> SQL
    EX --> CODE
    EX --> JUPYTER
    EX --> SEARCH
    EX --> DLC
    SQL --> DB
    CODE --> JUPYTER
    SEARCH --> KB
    DLC --> LAKE
```

### 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant AS as AgentService
    participant IR as IntentRecognizer
    participant PL as Planner
    participant EX as ExecutorAgent
    participant Tools as 工具层
    
    User->>AS: 发送查询请求
    AS->>IR: 意图识别
    IR->>IR: 三层对话管理
    IR->>PL: 传递完整意图
    PL->>PL: 生成执行计划
    PL->>EX: 传递子任务列表
    EX->>Tools: 执行工具调用
    Tools-->>EX: 返回执行结果
    EX-->>AS: 流式返回事件
    AS-->>User: 实时响应流
```

## 核心组件详解

### 1. AgentService (agent_service.py)

**功能**: 系统的主要服务入口，负责会话管理、状态维护和流式响应协调。

**主要类和方法**:

#### AgentService 类
```python
class AgentService:
    """
    数据科学代理的主要服务类，管理会话状态和协调各个组件
    
    Attributes:
        llm_client (OpenAIClient): LLM客户端实例
        mcp_manager (MCPManager): MCP工具管理器
        data_loader (DataLoader): 数据加载器
        executor_agent (ExecutorAgent): 任务执行代理
        session_states (Dict[str, AgentState]): 会话状态字典
        active_graph_tasks (Dict[str, asyncio.Task]): 活跃的图执行任务
    """
```

**核心方法**:
- `stream_chat()`: 主要的流式聊天接口，处理用户输入并返回事件流
- `request_stop()`: 请求停止指定会话的执行
- `_get_session_state()`: 获取会话状态
- `_save_session_state()`: 保存会话状态

### 2. GraphOrchestrator (graph_orchestrator.py)

**功能**: 基于 LangGraph 的流程编排器，定义了整个系统的执行流程图。

**核心函数**:

#### build_graph()
```python
def build_graph(llm_client: 'OpenAIClient', executor_agent: 'ExecutorAgent'):
    """
    构建 LangGraph 状态图，定义代理的执行流程
    
    Args:
        llm_client: OpenAI客户端实例
        executor_agent: 执行代理实例
        
    Returns:
        编译后的状态图应用
    """
```

**路由决策函数**:
- `should_plan_or_clarify_or_end()`: 决定是否需要规划、澄清或结束
- `after_planning_route()`: 规划后的路由决策

### 3. 状态管理 (state.py)

**功能**: 定义系统的共享状态结构。

#### AgentState 类型定义
```python
class AgentState(TypedDict):
    """
    定义图的共享状态结构
    
    Attributes:
        stop_requested (Optional[bool]): 停止请求标志
        current_user_input (Optional[str]): 当前用户输入
        conversation_history (List[Dict[str, str]]): 对话历史
        intent_recognizer_slot_state (Optional[Dict[str, Any]]): 意图识别状态
        identified_intent_name (Optional[str]): 识别的意图名称
        identified_intent_entities (Optional[Dict[str, Any]]): 提取的实体
        needs_clarification (bool): 是否需要澄清
        current_plan (Optional[Dict[str, Any]]): 当前执行计划
        jupyter_events (List[Dict[str, Any]]): Jupyter执行事件
    """
```

## 智能代理组件

### 1. IntentRecognizer (agents/intent_recognizer/)

**功能**: 三层对话管理的意图识别器，能够理解用户需求并收集必要信息。

#### 核心类结构

```mermaid
classDiagram
    class IntentRecognizer {
        -llm_client: OpenAIClient
        -language_identifier: LanguageIdentifier
        -intent_slot: IntentSlot
        +process_query() AsyncIterator
        +detect_user_language() str
        +is_intent_recognition_complete() bool
        +get_task_category() str
    }
    
    class IntentSlot {
        +content: IntentState
        +dataset: Dict
        +update_slot()
        +reset_for_new_intent()
    }
    
    class IntentState {
        +layer: int
        +conversation_count: int
        +task: TaskInfo
        +essential_info: Dict
        +supplementary_info: Dict
    }
    
    class TaskInfo {
        +category: str
        +specific_type: str
        +description: str
    }
    
    IntentRecognizer --> IntentSlot
    IntentSlot --> IntentState
    IntentState --> TaskInfo
```

**三层对话管理**:
1. **Layer 1**: 必要信息识别 (最多3轮对话)
2. **Layer 2**: 补充信息收集 (1轮对话)  
3. **Layer 3**: 完成确认

**支持的意图类型**:
- `data_science`: 机器学习/数据分析任务
- `nl_database_query`: 自然语言数据库查询
- `document_query`: 基于知识库的文档查询
- `complaint_or_report`: 投诉举报类问题
- `small_talk`: 简单闲聊

### 2. Planner (agents/planner/)

**功能**: 将用户意图转换为可执行的子任务计划。

#### 核心数据结构

```mermaid
classDiagram
    class Plan {
        +task_name: str
        +dataset_id: Optional[str]
        +subtasks: List[SubTask]
        +raw_user_query_for_context: Optional[str]
    }
    
    class SubTask {
        +idx: int
        +dep: List[int]
        +desc: str
        +adv_tool: Optional[str]
    }
    
    Plan --> SubTask
```

**核心函数**:
- `create_plan()`: 异步创建执行计划
- `create_plan_sync()`: 同步版本的计划创建

**工具组合策略**:
- 数据科学任务: `数据加载 → 数据处理 → 特征工程 → 模型训练 → 结果可视化`
- 数据库查询: `SQL生成 → 查询执行 → 结果返回`
- 文档查询: `语义检索 → 答案生成`

### 3. ExecutorAgent (agents/executor/)

**功能**: 执行具体的子任务，基于 FlexibleReactAgent 实现。

#### 执行流程图

```mermaid
flowchart TD
    A[接收子任务列表] --> B[初始化执行环境]
    B --> C[遍历子任务]
    C --> D{检查停止请求}
    D -->|是| E[停止执行]
    D -->|否| F[解析工具组合]
    F --> G[执行工具链]
    G --> H{工具执行成功?}
    H -->|是| I[记录结果]
    H -->|否| J[错误重试]
    J --> K{重试次数<3?}
    K -->|是| G
    K -->|否| L[标记失败]
    I --> M{还有子任务?}
    L --> M
    M -->|是| C
    M -->|否| N[完成执行]
```

**核心方法**:
- `execute_subtasks()`: 执行结构化计划中的所有子任务
- `initialize()`: 异步初始化执行环境

## 工具系统

### 工具架构

```mermaid
classDiagram
    class BaseTool {
        <<abstract>>
        +name: str
        +description: str
        +args_schema: Type[ToolInputSchema]
        +run() ToolOutputSchema
        +_execute() ToolOutputSchema*
    }
    
    class ToolInputSchema {
        <<BaseModel>>
    }
    
    class ToolOutputSchema {
        +status: str
        +observation: Dict[str, Any]
        +error_message: Optional[str]
    }
    
    class CodeGenTool {
        +name: "code_gen"
        +description: "生成可执行代码"
        +_execute() ToolOutputSchema
    }
    
    class CodeRunEnvTool {
        +name: "code_run_env"
        +description: "执行代码"
        +_execute() ToolOutputSchema
    }
    
    BaseTool <|-- CodeGenTool
    BaseTool <|-- CodeRunEnvTool
    BaseTool --> ToolInputSchema
    BaseTool --> ToolOutputSchema
```

### 可用工具列表

1. **SQL生成工具** (`generate_sql__generate_sql`)
   - 功能: 将自然语言转换为SQL查询
   - 输入: 自然语言描述
   - 输出: SQL语句、推理过程、涉及的表

2. **代码生成工具** (`nl2code__nl2code`)
   - 功能: 基于自然语言生成Python代码
   - 输入: 用户指令、环境依赖、全局变量等
   - 输出: Python代码和所需包列表

3. **Jupyter执行工具** (`jupyter__execute_code`)
   - 功能: 在远程Jupyter内核中执行代码
   - 输入: Python代码字符串
   - 输出: 执行结果和错误信息

4. **数据加载工具** (`jupyter__load_data_by_sql`)
   - 功能: 执行SQL查询并加载为DataFrame
   - 输入: SQL查询语句
   - 输出: 加载的数据框

5. **语义检索工具** (`aisearch__aisearch_retrieve`)
   - 功能: 智能文档检索
   - 输入: 查询关键词
   - 输出: 相关文档片段

6. **数据湖计算工具** (DLC/TCHouseD系列)
   - `DLCExecuteQuery`: 执行SQL任务
   - `DLCListTables`: 列出数据表
   - `DLCListDatabases`: 列出数据库
   - `DLCListEngines`: 列出数据引擎

## 数据管理

### DataLoader (utils/data_loader.py)

**功能**: 负责数据库模式信息的加载和表数据采样。

#### 核心方法

```python
class DataLoader:
    """
    数据加载器，使用MCP工具加载数据库模式信息
    
    Attributes:
        mcp_manager (MCPManager): MCP管理器实例
    """
    
    async def get_database_schema_async(self, dataset_id: str) -> Dict[str, Any]:
        """
        异步获取数据库模式信息
        
        Args:
            dataset_id: 数据集标识符
            
        Returns:
            包含数据库模式信息的字典
        """
    
    async def sample_table_data_async(self, table_name: str, database_name: str, 
                                    datasource_name: str, engine_name: str, 
                                    sample_size: int = 10) -> Dict[str, Any]:
        """
        异步采样表数据
        
        Args:
            table_name: 表名
            database_name: 数据库名
            datasource_name: 数据源名
            engine_name: 引擎名
            sample_size: 采样大小
            
        Returns:
            包含列名、样本数据和行数的字典
        """
```

### OpenAIClient (utils/openai_client.py)

**功能**: 封装OpenAI API调用，支持流式和非流式响应。

#### 核心方法

```python
class OpenAIClient:
    """
    OpenAI客户端封装类
    
    Attributes:
        client (AsyncOpenAI): 异步OpenAI客户端
        default_model (str): 默认模型名称
        temperature (float): 温度参数
    """
    
    async def generate(self, messages: List[Dict[str, Any]], 
                      tools: Optional[List[Dict[str, Any]]] = None,
                      tool_choice: Union[str, Dict[str, Any]] = "auto",
                      model: Optional[str] = None) -> ChatCompletionMessage:
        """
        非流式生成响应，支持工具调用
        
        Args:
            messages: 对话消息列表
            tools: 可用工具列表
            tool_choice: 工具选择策略
            model: 模型名称（可选）
            
        Returns:
            聊天完成消息对象
        """
    
    async def generate_stream(self, messages: List[Dict[str, Any]], 
                            tools: Optional[List[Dict[str, Any]]] = None,
                            tool_choice: Union[str, Dict[str, Any]] = "auto",
                            model: Optional[str] = None) -> AsyncIterator[ChatCompletionChunk]:
        """
        流式生成响应
        
        Args:
            messages: 对话消息列表
            tools: 可用工具列表
            tool_choice: 工具选择策略
            model: 模型名称（可选）
            
        Returns:
            异步迭代器，产生响应块
        """
```

## 执行流程

### 完整执行流程图

```mermaid
flowchart TD
    Start([用户输入]) --> Input[获取用户输入]
    Input --> Intent[意图识别]
    
    Intent --> Decision{需要澄清?}
    Decision -->|是| Clarify[澄清问题]
    Decision -->|否| Plan[生成计划]
    
    Clarify --> Output[输出响应]
    
    Plan --> Execute[执行子任务]
    Execute --> Tool1[工具调用1]
    Tool1 --> Tool2[工具调用2]
    Tool2 --> ToolN[工具调用N]
    ToolN --> Finish[完成子任务]
    
    Finish --> More{还有子任务?}
    More -->|是| Execute
    More -->|否| Summary[生成总结]
    
    Summary --> Output
    Output --> End([结束])
    
    style Intent fill:#e1f5fe
    style Plan fill:#f3e5f5
    style Execute fill:#e8f5e8
    style Output fill:#fff3e0
```

### 意图识别流程

```mermaid
stateDiagram-v2
    [*] --> Layer1: 用户输入
    
    state Layer1 {
        [*] --> 识别基本意图
        识别基本意图 --> 提取必要信息
        提取必要信息 --> 检查完整性
        检查完整性 --> [*]: 信息充足
        检查完整性 --> 询问补充: 信息不足
        询问补充 --> [*]: 达到3轮上限
    }
    
    Layer1 --> Layer2: 需要补充信息
    
    state Layer2 {
        [*] --> 收集补充信息
        收集补充信息 --> [*]: 1轮完成
    }
    
    Layer2 --> Layer3: 信息收集完成
    
    state Layer3 {
        [*] --> 确认任务完整性
        确认任务完整性 --> [*]: 准备执行
    }
    
    Layer3 --> [*]: 进入规划阶段
```

## 工具调用机制

### 工具调用流程

```mermaid
sequenceDiagram
    participant EX as ExecutorAgent
    participant FA as FlexibleReactAgent
    participant MCP as MCPManager
    participant Tool as 具体工具
    
    EX->>FA: 传递子任务
    FA->>FA: 解析工具组合
    loop 工具链执行
        FA->>MCP: 调用工具
        MCP->>Tool: 执行具体工具
        Tool-->>MCP: 返回结果
        MCP-->>FA: 返回工具结果
        FA->>FA: 处理结果
    end
    FA->>FA: 调用finish工具
    FA-->>EX: 返回子任务结果
```

### 常见工具组合

1. **数据科学任务**:
   ```
   数据加载: generate_sql → jupyter__load_data_by_sql → finish
   数据处理: nl2code → jupyter__execute_code → finish
   特征工程: nl2code → jupyter__execute_code → finish
   模型训练: nl2code → jupyter__execute_code → finish
   结果可视化: nl2code → jupyter__execute_code → finish
   ```

2. **数据库查询任务**:
   ```
   数据读取: generate_sql → DLCExecuteQuery → finish
   ```

3. **文档查询任务**:
   ```
   知识库问答: aisearch_retrieve → finish
   ```

4. **投诉举报任务**:
   ```
   语义检索: aisearch_retrieve → finish
   数据处理: load_data_and_join_table → finish
   数据分析: nl2code → jupyter__execute_code → finish
   ```

## 错误处理和恢复

### 错误处理策略

```mermaid
flowchart TD
    Error[工具执行错误] --> Analyze[分析错误原因]
    Analyze --> Type{错误类型}
    
    Type -->|参数错误| FixParam[修正参数]
    Type -->|SQL错误| FixSQL[修正SQL]
    Type -->|代码错误| FixCode[修正代码]
    Type -->|网络错误| Retry[重试请求]
    
    FixParam --> Execute[重新执行]
    FixSQL --> Execute
    FixCode --> Execute
    Retry --> Execute
    
    Execute --> Success{执行成功?}
    Success -->|是| Continue[继续下一步]
    Success -->|否| Count{重试次数<3?}
    
    Count -->|是| Analyze
    Count -->|否| Fail[标记失败]
    
    Continue --> End[完成]
    Fail --> End
```

## 配置和部署

### 环境变量配置

- `EXECUTION_MODE`: 执行模式 (ray/local)
- `LOG_LEVEL`: 日志级别
- `DUMP_TO_NOTEBOOK`: 是否导出到Notebook
- `START_AISEARCH_TASK`: 是否启动AI搜索任务

### 依赖关系

```mermaid
graph LR
    subgraph "外部依赖"
        OpenAI[OpenAI API]
        LangGraph[LangGraph]
        Jupyter[Jupyter Kernel]
        MCP[MCP服务]
    end
    
    subgraph "内部模块"
        Common[common模块]
        Infra[infra模块]
        Domain[domain模块]
    end
    
    AgentService --> OpenAI
    AgentService --> LangGraph
    ExecutorAgent --> Jupyter
    ExecutorAgent --> MCP
    AgentService --> Common
    AgentService --> Infra
```

## 性能监控

### 指标收集

- **任务执行时间**: 每个子任务的执行耗时
- **工具调用次数**: 各类工具的调用统计
- **成功率**: 任务完成成功率
- **Token使用量**: LLM调用的Token消耗

### 日志记录

系统采用结构化日志记录，包含：
- 会话ID追踪
- 任务执行状态
- 工具调用详情
- 错误堆栈信息

## 扩展指南

### 添加新工具

1. 继承 `BaseTool` 类
2. 定义输入输出模式
3. 实现 `_execute()` 方法
4. 在工具注册表中注册

### 添加新意图类型

1. 在 `intent_schemas.py` 中定义新的意图类别
2. 在 `prompt.py` 中添加识别规则
3. 在 `planner_prompts.py` 中添加规划模板
4. 更新工具组合策略

### 性能优化建议

1. **并发执行**: 利用异步特性并行执行独立的子任务
2. **缓存机制**: 缓存数据库模式和常用查询结果
3. **资源池化**: 复用Jupyter内核和数据库连接
4. **智能重试**: 根据错误类型采用不同的重试策略

## 维护指南

### 代码规范

- 遵循PEP 8编码规范
- 每行代码不超过79字符
- 添加详细的函数级注释和docstring
- 使用类型提示增强代码可读性

### 测试策略

- 单元测试覆盖核心业务逻辑
- 集成测试验证组件间交互
- 端到端测试确保用户场景正常

### 监控和告警

- 设置关键指标的监控阈值
- 配置异常情况的告警机制
- 定期检查系统健康状态

## 详细API参考

### AgentService API

#### stream_chat 方法
```python
async def stream_chat(self, user_input: str, session_id: str,
                     record_id: str = None, ctx: Optional[Any] = None,
                     req_context: Optional[str] = None) -> AsyncIterator[AgentEvent]:
    """
    主要的流式聊天接口

    Args:
        user_input: 用户输入文本
        session_id: 会话标识符
        record_id: 记录标识符（可选）
        ctx: 上下文对象（可选）
        req_context: 请求上下文（可选）

    Yields:
        AgentEvent: 各种类型的代理事件

    Events:
        - MessageEvent: 用户消息事件
        - ThinkEvent: 思考过程事件
        - TextEvent: 文本输出事件
        - TaskListEvent: 任务列表事件
        - JupyterEvent: Jupyter执行事件
        - ErrorEvent: 错误事件
        - FinalSummaryEvent: 最终总结事件
    """
```

#### request_stop 方法
```python
def request_stop(self, session_id: str) -> bool:
    """
    请求停止指定session的执行

    Args:
        session_id: 会话ID

    Returns:
        bool: 是否成功设置停止标志

    Implementation:
        1. 设置会话状态中的stop_requested标志
        2. 取消活跃的graph任务
        3. 记录停止请求日志
    """
```

### IntentRecognizer API

#### process_query 方法
```python
async def process_query(self, user_input: str,
                       conversation_history: List[Dict[str, str]] = None,
                       ctx: Optional[Dict[str, Any]] = None,
                       state: Optional[Dict[str, Any]] = None) -> AsyncIterator[Union[ThinkEvent, TextEvent, MessageEvent]]:
    """
    流式处理用户输入，返回事件流

    Args:
        user_input: 用户输入文本
        conversation_history: 对话历史记录
        ctx: 上下文信息
        state: 状态信息

    Yields:
        Union[ThinkEvent, TextEvent, MessageEvent]: 不同类型的事件

    Process:
        1. 语言检测
        2. 权限验证（投诉举报类）
        3. 三层对话管理
        4. 意图识别和实体提取
        5. 状态更新
    """
```

#### 状态管理方法
```python
def get_current_slot_state(self) -> Dict[str, Any]:
    """获取当前槽位状态的详细信息"""

def is_intent_recognition_complete(self) -> bool:
    """检查意图识别是否完成（layer == 3）"""

def get_task_category(self) -> Optional[str]:
    """获取任务类别"""

def reset_intent(self):
    """重置意图识别器状态"""
```

### Planner API

#### create_plan 函数
```python
async def create_plan(llm_client: Any, intent_name: str,
                     intent_entities: Dict[str, Any], user_query: str,
                     database_schema: Optional[Dict[str, Any]] = None,
                     rag_examples: Optional[List[Dict[str, Any]]] = None,
                     feedback_on_failed_plan: Optional[Dict[str, Any]] = None,
                     user_language: str = 'zh',
                     ctx: Optional[Any] = None) -> Plan:
    """
    创建执行计划

    Args:
        llm_client: LLM客户端
        intent_name: 识别的意图名称
        intent_entities: 提取的实体参数
        user_query: 用户原始查询
        database_schema: 数据库模式（可选）
        rag_examples: RAG示例（可选）
        feedback_on_failed_plan: 失败反馈（可选）
        user_language: 用户语言
        ctx: 上下文对象（可选）

    Returns:
        Plan: 验证后的执行计划

    Process:
        1. 根据意图类型选择提示词模板
        2. 格式化输入参数
        3. 调用LLM生成计划
        4. 解析和验证JSON输出
        5. 创建Plan对象
    """
```

### ExecutorAgent API

#### execute_subtasks 方法
```python
async def execute_subtasks(self, structured_plan: Dict[str, Any],
                          agent_state: 'AgentState' = None) -> AsyncGenerator[Dict[str, Any], None]:
    """
    执行结构化计划中的所有子任务

    Args:
        structured_plan: 包含子任务的结构化计划
        agent_state: 完整的agent状态

    Yields:
        Dict[str, Any]: 执行事件

    Events:
        - task_start: 子任务开始
        - tool_call: 工具调用
        - tool_result: 工具结果
        - task_complete: 子任务完成
        - final_result: 最终结果
        - error_event: 错误事件

    Process:
        1. 初始化FlexibleReactAgent
        2. 设置系统提示词和环境上下文
        3. 遍历执行所有子任务
        4. 处理停止请求和错误恢复
        5. 收集执行结果和事件
    """
```

## 数据结构详解

### IntentSlot 数据结构

```mermaid
classDiagram
    class IntentSlot {
        +content: IntentState
        +dataset: Dict[str, Any]
        +update_slot(updates: Dict[str, Any])
        +reset_for_new_intent()
        +model_dump() Dict[str, Any]
    }

    class IntentState {
        +layer: int
        +conversation_count: int
        +task: Optional[TaskInfo]
        +essential_info: Dict[str, Any]
        +supplementary_info: Dict[str, Any]
        +metadata: Dict[str, Any]
    }

    class TaskInfo {
        +category: str
        +specific_type: str
        +description: str
    }

    IntentSlot --> IntentState
    IntentState --> TaskInfo
```

### Plan 数据结构

```mermaid
classDiagram
    class Plan {
        +task_name: str
        +dataset_id: Optional[str]
        +subtasks: List[SubTask]
        +raw_user_query_for_context: Optional[str]
        +model_dump() Dict[str, Any]
    }

    class SubTask {
        +idx: int
        +dep: List[int]
        +desc: str
        +adv_tool: Optional[str]
    }

    Plan --> SubTask
```

### AgentState 完整结构

```python
class AgentState(TypedDict):
    # 控制标志
    stop_requested: Optional[bool]

    # 输入和对话历史
    current_user_input: Optional[str]
    conversation_history: List[Dict[str, str]]

    # 用户反馈相关
    user_feedback_on_last_result: Optional[str]

    # 意图识别状态
    intent_recognizer_slot_state: Optional[Dict[str, Any]]

    # 最终识别的意图详情
    identified_intent_name: Optional[str]
    identified_intent_entities: Optional[Dict[str, Any]]

    # 对话流程控制标志
    needs_clarification: bool

    # 规划结果
    current_plan: Optional[Dict[str, Any]]

    # 执行相关
    jupyter_events: List[Dict[str, Any]]
    execution_error: Optional[str]

    # 数据和上下文
    database_schema: Optional[Dict[str, Any]]
    active_dataset_id: Optional[str]
    detected_language: Optional[str]
    ctx: Optional[Any]
    mcp_manager: Optional[Any]
```

## 事件系统

### 事件类型定义

```mermaid
classDiagram
    class AgentEvent {
        <<abstract>>
        +type: str
        +timestamp: datetime
    }

    class MessageEvent {
        +content: str
        +role: str
    }

    class ThinkEvent {
        +content: str
        +reasoning_type: str
    }

    class TextEvent {
        +content: str
    }

    class TaskListEvent {
        +tasks: List[Dict]
        +current_task_index: int
    }

    class JupyterEvent {
        +cell_id: str
        +code: str
        +outputs: List[Dict]
        +execution_time: float
    }

    class ErrorEvent {
        +error_message: str
        +error_type: str
        +context: Dict[str, Any]
    }

    class FinalSummaryEvent {
        +summary: str
        +task_results: List[Dict]
    }

    AgentEvent <|-- MessageEvent
    AgentEvent <|-- ThinkEvent
    AgentEvent <|-- TextEvent
    AgentEvent <|-- TaskListEvent
    AgentEvent <|-- JupyterEvent
    AgentEvent <|-- ErrorEvent
    AgentEvent <|-- FinalSummaryEvent
```

### 事件流处理

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant AS as AgentService
    participant GO as GraphOrchestrator
    participant Nodes as 图节点

    Client->>AS: 发起stream_chat请求
    AS->>GO: 启动图执行

    loop 图节点执行
        GO->>Nodes: 执行节点
        Nodes-->>AS: 产生事件
        AS-->>Client: 流式返回事件
    end

    GO-->>AS: 图执行完成
    AS-->>Client: 发送最终事件
```

## 安全和权限

### 权限控制机制

```mermaid
flowchart TD
    Request[用户请求] --> Auth[权限验证]
    Auth --> Check{检查req_context}

    Check -->|无权限信息| Default[默认权限]
    Check -->|有权限信息| Parse[解析白名单]

    Parse --> Advanced{包含AdvancedFeature?}
    Advanced -->|是| Allow[允许高级功能]
    Advanced -->|否| Restrict[限制功能]

    Default --> Basic[基础功能]
    Allow --> Execute[执行请求]
    Restrict --> Execute
    Basic --> Execute

    Execute --> Response[返回响应]
```

### 安全措施

1. **输入验证**: 对所有用户输入进行严格验证
2. **权限控制**: 基于白名单的功能权限管理
3. **代码沙箱**: Jupyter执行环境隔离
4. **资源限制**: 限制执行时间和资源使用
5. **审计日志**: 记录所有关键操作

## 故障排除

### 常见问题和解决方案

#### 1. 意图识别失败
**症状**: 系统无法正确识别用户意图
**原因**:
- 用户输入过于模糊
- 提示词模板不匹配
- LLM响应格式错误

**解决方案**:
- 检查用户输入的完整性
- 验证提示词模板
- 增加JSON解析的容错处理

#### 2. 工具调用失败
**症状**: 工具执行返回错误
**原因**:
- 参数格式不正确
- 外部服务不可用
- 权限不足

**解决方案**:
- 验证工具参数格式
- 检查外部服务状态
- 确认权限配置

#### 3. Jupyter执行超时
**症状**: 代码执行长时间无响应
**原因**:
- 代码逻辑复杂度过高
- 数据量过大
- 内核资源不足

**解决方案**:
- 优化代码逻辑
- 限制数据处理规模
- 增加执行超时设置

### 调试工具

#### 日志分析
```bash
# 查看特定会话的日志
grep "session_id:12345" /var/log/agent.log

# 查看错误日志
grep "ERROR" /var/log/agent.log | tail -50

# 查看工具调用日志
grep "tool_call" /var/log/agent.log
```

#### 状态检查
```python
# 检查会话状态
agent_service.session_states.get(session_id)

# 检查活跃任务
agent_service.active_graph_tasks.keys()

# 检查MCP连接状态
mcp_manager.get_server_status()
```

## 最佳实践

### 开发最佳实践

1. **模块化设计**: 保持组件间的低耦合
2. **异步优先**: 优先使用异步方法提高性能
3. **错误处理**: 实现完善的错误处理和恢复机制
4. **日志记录**: 添加详细的结构化日志
5. **类型提示**: 使用完整的类型注解

### 运维最佳实践

1. **监控指标**: 设置关键性能指标监控
2. **资源管理**: 合理配置内存和CPU资源
3. **备份策略**: 定期备份重要数据和配置
4. **版本管理**: 使用语义化版本控制
5. **文档维护**: 保持技术文档的及时更新

### 性能优化

#### 缓存策略
```mermaid
graph LR
    subgraph "缓存层次"
        L1[L1: 内存缓存<br/>会话状态]
        L2[L2: Redis缓存<br/>数据库模式]
        L3[L3: 文件缓存<br/>查询结果]
    end

    Request[请求] --> L1
    L1 -->|缓存未命中| L2
    L2 -->|缓存未命中| L3
    L3 -->|缓存未命中| DB[(数据源)]
```

#### 并发优化
- 使用异步I/O减少阻塞
- 并行执行独立的数据采样任务
- 工具调用的批量处理
- 连接池复用

## 扩展开发指南

### 添加新的意图类型

1. **定义意图模式**
```python
# 在 intent_schemas.py 中添加
class NewIntentInfo(BaseModel):
    category: str = "new_intent_type"
    specific_parameters: Dict[str, Any] = Field(default_factory=dict)
```

2. **更新识别提示词**
```python
# 在 prompt.py 中添加识别规则
NEW_INTENT_EXAMPLES = """
示例:
- "新功能请求示例1"
- "新功能请求示例2"
"""
```

3. **添加规划模板**
```python
# 在 planner_prompts.py 中添加
NEW_INTENT_TEMPLATE = """
新意图类型的子任务划分模板...
"""
```

### 添加新工具

1. **继承基础工具类**
```python
class NewTool(BaseTool):
    name: str = "new_tool"
    description: str = "新工具的描述"
    args_schema = NewToolInput

    def _execute(self, **kwargs) -> ToolOutputSchema:
        # 实现具体的工具逻辑
        pass
```

2. **注册工具**
```python
# 在 __init__.py 中注册
AVAILABLE_TOOLS = {
    "new_tool": NewTool,
    # ... 其他工具
}
```

3. **更新工具组合**
```python
# 在 planner_prompts.py 中添加工具组合示例
"新任务类型"任务工具组合: new_tool + finish
```

## 测试指南

### 单元测试示例

```python
import pytest
from unittest.mock import AsyncMock, MagicMock
from infra.datascience_agent.agents.intent_recognizer.intent_recognizer import IntentRecognizer

@pytest.mark.asyncio
async def test_intent_recognition():
    """测试意图识别功能"""
    # 模拟LLM客户端
    mock_llm_client = AsyncMock()
    mock_llm_client.generate_stream.return_value = async_mock_stream()

    # 创建意图识别器
    recognizer = IntentRecognizer(mock_llm_client)

    # 测试用户输入
    user_input = "我想分析销售数据的趋势"

    # 执行意图识别
    events = []
    async for event in recognizer.process_query(user_input):
        events.append(event)

    # 验证结果
    assert len(events) > 0
    assert recognizer.get_task_category() == "data_science"
```

### 集成测试示例

```python
@pytest.mark.asyncio
async def test_end_to_end_data_science_task():
    """端到端数据科学任务测试"""
    # 设置测试环境
    agent_service = AgentService(mock_mcp_manager)

    # 模拟用户输入
    user_input = "请分析产品销售数据并预测下个月的销量"
    session_id = "test_session_001"

    # 执行完整流程
    events = []
    async for event in agent_service.stream_chat(user_input, session_id):
        events.append(event)

    # 验证执行结果
    assert any(event.type == "FinalSummaryEvent" for event in events)
    assert agent_service.session_states[session_id]['identified_intent_name'] == "data_science"
```

## 部署配置

### Docker 配置示例

```dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 设置环境变量
ENV EXECUTION_MODE=ray
ENV LOG_LEVEL=INFO
ENV PYTHONPATH=/app

# 启动服务
CMD ["python", "-m", "infra.endpoint.main"]
```

### Kubernetes 部署配置

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intellix-ds-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: intellix-ds-agent
  template:
    metadata:
      labels:
        app: intellix-ds-agent
    spec:
      containers:
      - name: agent
        image: intellix-ds-agent:latest
        env:
        - name: EXECUTION_MODE
          value: "ray"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
```

---

*本文档版本: v1.0*
*最后更新: 2025-08-14*
*维护团队: Intellix Data Science Team*

# 数据科学智能体技术文档

## 目录
1. [系统概述](#系统概述)
2. [架构设计](#架构设计)
3. [核心组件详解](#核心组件详解)
4. [数据流与状态管理](#数据流与状态管理)
5. [API接口规范](#api接口规范)
6. [部署与运维](#部署与运维)
7. [扩展指南](#扩展指南)

---

## 系统概述

数据科学智能体是一个基于LangGraph构建的AI驱动数据分析系统，支持自然语言查询、数据科学任务执行、知识库问答等功能。系统采用模块化设计，具备高度的可扩展性和可维护性。

### 核心能力
- **自然语言到SQL转换**：支持中英文自然语言查询数据库
- **数据科学任务**：机器学习、数据可视化、统计分析
- **知识库问答**：基于文档的智能问答系统
- **多语言支持**：中英文双语处理
- **流式响应**：实时输出执行结果和中间状态

---

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        UI[用户界面]
        API[API网关]
    end
    
    subgraph "服务层"
        AS[AgentService<br/>智能体服务]
        GR[GraphOrchestrator<br/>图编排器]
    end
    
    subgraph "智能体核心"
        IR[IntentRecognizer<br/>意图识别器]
        PL[Planner<br/>规划器]
        EX[Executor<br/>执行器]
        SM[Summarizer<br/>总结器]
    end
    
    subgraph "工具层"
        MCP[MCP管理器]
        JL[Jupyter环境]
        DB[数据库连接器]
        KB[知识库检索]
    end
    
    subgraph "基础设施"
        LLM[大语言模型]
        CACHE[缓存系统]
        LOG[日志系统]
    end
    
    UI --> API
    API --> AS
    AS --> GR
    GR --> IR
    GR --> PL
    GR --> EX
    GR --> SM
    
    IR --> MCP
    PL --> MCP
    EX --> MCP
    EX --> JL
    EX --> DB
    EX --> KB
    
    MCP --> LLM
    MCP --> CACHE
    MCP --> LOG
```

### 组件关系图

```mermaid
classDiagram
    class AgentService {
        -session_states: Dict
        -active_graph_tasks: Dict
        +stream_chat()
        +request_stop()
    }
    
    class GraphOrchestrator {
        +build_graph()
        +should_plan_or_clarify_or_end()
    }
    
    class IntentRecognizer {
        -llm_client: OpenAIClient
        +process_query()
        +get_task_category()
    }
    
    class Planner {
        -llm_client: OpenAIClient
        +create_plan()
    }
    
    class Executor {
        -llm_client: OpenAIClient
        -mcp_manager: MCPManager
        +execute_subtasks()
    }
    
    class State {
        +current_user_input: str
        +conversation_history: List
        +identified_intent_name: str
        +current_plan: Dict
        +jupyter_events: List
    }
    
    AgentService --> GraphOrchestrator
    GraphOrchestrator --> IntentRecognizer
    GraphOrchestrator --> Planner
    GraphOrchestrator --> Executor
    AgentService --> State
```

---

## 核心组件详解

### 1. AgentService - 智能体服务层

**文件位置**: [`agent_service.py`](agent_service.py)

#### 核心职责
- 管理用户会话状态
- 协调整个智能体生命周期
- 处理流式响应和取消操作
- 集成外部依赖（MCP、LLM等）

#### 关键类和方法

```python
class AgentService:
    """
    智能体服务主类，负责：
    1. 会话状态管理
    2. 图执行协调
    3. 流式响应处理
    4. 错误处理和恢复
    """
    
    def __init__(self, mcp_manager: MCPManager):
        """
        初始化AgentService
        
        Args:
            mcp_manager: MCP管理器实例，提供工具调用能力
        """
        pass
    
    async def stream_chat(self, ctx, query, session_id, record_id, ...) -> AsyncIterator[AgentEvent]:
        """
        流式对话接口
        
        处理流程：
        1. 初始化会话状态
        2. 意图识别
        3. 图执行
        4. 结果总结
        5. 状态清理
        
        Returns:
            异步事件流，包含：消息、思考、任务列表
# Intellix Data Science Agent 类和方法详细参考

## 目录结构概览

```
infra/datascience_agent/
├── agent_service.py              # 主服务类
├── graph_orchestrator.py         # 流程编排器
├── graph_nodes.py                # 图节点实现
├── state.py                      # 状态定义
├── summary_prompt.py             # 总结提示词
├── agents/                       # 智能代理模块
│   ├── intent_recognizer/        # 意图识别代理
│   │   ├── intent_recognizer.py  # 主识别器类
│   │   ├── intent_schemas.py     # 数据模式定义
│   │   └── prompt.py             # 提示词模板
│   ├── planner/                  # 任务规划代理
│   │   ├── planner.py            # 规划器实现
│   │   ├── planner_schemas.py    # 规划数据模式
│   │   └── planner_prompts.py    # 规划提示词
│   ├── executor/                 # 任务执行代理
│   │   ├── executor.py           # 执行器主类
│   │   ├── flexible_react_agent.py # 灵活反应代理
│   │   ├── executor_prompt.py    # 执行提示词
│   │   ├── custom_tool.py        # 自定义工具
│   │   └── utils.py              # 执行工具函数
│   ├── tools/                    # 工具系统
│   │   ├── base_tool.py          # 工具基类
│   │   ├── code_gen_tool.py      # 代码生成工具
│   │   └── code_run_env_tool.py  # 代码执行工具
│   └── mock_stream.py            # 模拟流处理
└── utils/                        # 工具模块
    ├── data_loader.py            # 数据加载器
    └── openai_client.py          # OpenAI客户端
```

## 核心类详细说明

### 1. AgentService 类

**文件**: `agent_service.py`

```python
class AgentService:
    """
    数据科学代理的主要服务类，作为整个系统的入口点
    
    职责:
    - 管理用户会话状态
    - 协调各个智能代理组件
    - 处理流式响应
    - 管理异步任务生命周期
    
    设计模式:
    - 门面模式: 为复杂的子系统提供统一接口
    - 单例模式: 每个MCP管理器对应一个服务实例
    - 观察者模式: 通过事件流通知客户端状态变化
    """
```

#### 构造方法详解
```python
def __init__(self, mcp_manager: MCPManager):
    """
    初始化AgentService实例
    
    参数验证:
        - mcp_manager不能为None
        - 必须是有效的MCPManager实例
    
    初始化步骤:
        1. 存储MCP管理器引用
        2. 从配置创建OpenAI客户端
        3. 初始化数据加载器（传入MCP管理器）
        4. 创建执行代理实例
        5. 构建LangGraph状态图
        6. 初始化会话状态字典
        7. 创建初始状态模板
        8. 初始化活跃任务字典
    
    异常处理:
        - ValueError: MCP管理器为None
        - ConfigurationError: 配置信息缺失
        - InitializationError: 组件初始化失败
    """
```

#### 核心方法详解

##### stream_chat 方法
```python
async def stream_chat(self, user_input: str, session_id: str, 
                     record_id: str = None, ctx: Optional[Any] = None, 
                     req_context: Optional[str] = None) -> AsyncIterator[AgentEvent]:
    """
    主要的流式聊天接口
    
    执行流程:
        1. 参数验证和日志记录
        2. 获取或创建会话状态
        3. 设置用户输入和上下文信息
        4. 预处理意图识别（如果需要）
        5. 启动LangGraph状态图执行
        6. 流式处理图节点事件
        7. 清理会话资源
    
    状态管理:
        - 每个session_id对应独立的状态
        - 状态在内存中维护，支持持久化
        - 自动清理过期会话
    
    事件流处理:
        - 实时转发图节点产生的事件
        - 支持事件过滤和转换
        - 处理背压和流控制
    
    错误恢复:
        - 捕获图执行异常
        - 生成错误事件
        - 保持会话状态一致性
    
    性能考虑:
        - 使用异步I/O避免阻塞
        - 支持并发会话处理
        - 内存使用优化
    """
```

##### 会话管理方法
```python
def _get_session_state(self, session_id: str) -> AgentState:
    """
    获取会话状态，如果不存在则创建新状态
    
    实现细节:
        1. 检查session_states字典
        2. 如果存在，直接返回
        3. 如果不存在，从模板创建新状态
        4. 记录状态创建日志
    
    线程安全:
        该方法不是线程安全的，需要在调用层保证同步
    """

def _save_session_state(self, session_id: str, state: AgentState):
    """
    保存会话状态到内存
    
    扩展点:
        - 可以扩展为持久化到Redis
        - 支持状态版本控制
        - 添加状态压缩
    """
```

### 2. IntentRecognizer 类

**文件**: `agents/intent_recognizer/intent_recognizer.py`

```python
class IntentRecognizer:
    """
    三层对话管理的意图识别器
    
    核心特性:
    - 多轮对话管理
    - 语言自动检测
    - 权限验证集成
    - 流式响应处理
    
    设计原则:
    - 状态机模式: 通过layer管理对话状态
    - 策略模式: 根据意图类型选择不同处理策略
    - 模板方法模式: 定义对话流程框架
    """
```

#### 核心方法详解

##### process_query 方法
```python
async def process_query(self, user_input: str, 
                       conversation_history: List[Dict[str, str]] = None,
                       ctx: Optional[Dict[str, Any]] = None,
                       state: Optional[Dict[str, Any]] = None) -> AsyncIterator[Union[ThinkEvent, TextEvent, MessageEvent]]:
    """
    流式处理用户输入的核心方法
    
    三层对话管理:
        Layer 1 (必要信息识别):
        - 最多3轮对话
        - 识别基本意图类型
        - 收集必要参数
        - 判断信息完整性
        
        Layer 2 (补充信息收集):
        - 1轮对话
        - 收集优化参数
        - 确认用户偏好
        - 完善任务描述
        
        Layer 3 (完成确认):
        - 最终确认
        - 生成完整任务描述
        - 准备进入规划阶段
    
    流式处理机制:
        1. JSON阶段: 捕获结构化输出
        2. THINKING阶段: 捕获推理过程
        3. MESSAGE阶段: 捕获用户消息
    
    权限验证:
        - 检查req_context白名单
        - 验证AdvancedFeature权限
        - 处理权限拒绝情况
    
    语言处理:
        - 自动检测用户语言
        - 支持中英文切换
        - 语言信息传递给下游组件
    """
```

##### 状态查询方法
```python
def is_intent_recognition_complete(self) -> bool:
    """
    检查意图识别是否完成
    
    完成条件:
        1. layer == 3 (达到第三层)
        2. 或者任务类型为"end_conversation"
    
    特殊处理:
        - 结束对话意图立即完成
        - 错误情况下的强制完成
    """

def get_task_category(self) -> Optional[str]:
    """
    获取识别的任务类别
    
    支持的类别:
        - data_science: 数据科学和机器学习任务
        - nl_database_query: 自然语言数据库查询
        - document_query: 基于知识库的文档查询
        - complaint_or_report: 投诉举报数据分析
        - small_talk: 日常对话
    
    返回值处理:
        - 从TaskInfo对象中提取category
        - 处理不同的数据结构格式
        - 返回标准化的类别名称
    """
```

### 3. Planner 相关类

**文件**: `agents/planner/planner.py`

#### create_plan 函数
```python
async def create_plan(llm_client: Any, intent_name: str,
                     intent_entities: Dict[str, Any], user_query: str,
                     database_schema: Optional[Dict[str, Any]] = None,
                     rag_examples: Optional[List[Dict[str, Any]]] = None,
                     feedback_on_failed_plan: Optional[Dict[str, Any]] = None,
                     user_language: str = 'zh',
                     ctx: Optional[Any] = None) -> Plan:
    """
    异步创建执行计划
    
    规划策略:
        1. 根据意图类型选择模板
        2. 分析任务复杂度
        3. 确定工具组合
        4. 生成子任务序列
        5. 验证计划可行性
    
    模板选择逻辑:
        - data_science → 数据科学模板
        - nl_database_query → 数据库查询模板
        - document_query → 文档查询模板
        - complaint_or_report → 投诉举报模板
    
    子任务分解原则:
        - 每个子任务代表一个完整的功能单元
        - 避免过度细分（如单独的"导入库"任务）
        - 保持任务间的逻辑依赖关系
        - 确保每个子任务都有明确的工具组合
    
    工具组合验证:
        - 检查工具链的完整性
        - 验证工具间的兼容性
        - 确保以finish工具结束
    """
```

#### Plan 和 SubTask 类
```python
class Plan(BaseModel):
    """
    执行计划数据模型
    
    验证规则:
        - task_name不能为空
        - subtasks必须是有效的SubTask列表
        - dataset_id必须匹配已知数据集
    
    序列化:
        - 支持JSON序列化
        - 保持字段顺序
        - 处理嵌套对象
    """

class SubTask(BaseModel):
    """
    子任务数据模型
    
    依赖管理:
        - dep字段定义前置依赖
        - 支持复杂的依赖图
        - 自动检测循环依赖
    
    工具组合格式:
        - 使用" + "分隔工具名称
        - 必须以"finish"结尾
        - 支持条件工具选择
    """
```

### 4. ExecutorAgent 类

**文件**: `agents/executor/executor.py`

```python
class ExecutorAgent:
    """
    增强的执行器代理
    
    架构特点:
    - 委托模式: 委托FlexibleReactAgent执行具体任务
    - 适配器模式: 适配不同的工具接口
    - 装饰器模式: 增强基础执行功能
    
    生命周期管理:
    - 延迟初始化: 支持异步初始化
    - 资源清理: 自动清理执行资源
    - 状态恢复: 支持执行状态恢复
    """
```

#### 核心方法详解

##### execute_subtasks 方法
```python
async def execute_subtasks(self, structured_plan: Dict[str, Any], 
                          agent_state: 'AgentState' = None) -> AsyncGenerator[Dict[str, Any], None]:
    """
    执行结构化计划中的所有子任务
    
    执行策略:
        1. 顺序执行: 按idx顺序执行子任务
        2. 依赖检查: 验证前置依赖完成
        3. 错误隔离: 单个子任务失败不影响其他任务
        4. 状态同步: 实时更新执行状态
    
    事件生成:
        - task_start: 子任务开始
        - tool_call: 每次工具调用
        - tool_result: 工具执行结果
        - task_complete: 子任务完成
        - error_event: 错误发生
        - final_result: 最终结果
    
    停止处理:
        - 检查stop_requested标志
        - 优雅停止当前子任务
        - 清理未完成的任务
    
    资源管理:
        - 自动管理Jupyter内核
        - 清理临时文件
        - 释放数据库连接
    """
```

### 5. FlexibleReactAgent 类

**文件**: `agents/executor/flexible_react_agent.py`

```python
class FlexibleReactAgent:
    """
    灵活的反应式代理，实现ReAct模式的工具调用
    
    核心特性:
    - ReAct模式: Reasoning + Acting
    - 工具链执行: 支持复杂的工具组合
    - 错误重试: 智能错误恢复机制
    - 上下文管理: 维护执行上下文
    
    执行模式:
    - 流式执行: 实时输出执行过程
    - 批量执行: 支持批量工具调用
    - 条件执行: 根据结果决定下一步
    """
```

#### 关键方法详解

##### process_subtask 方法
```python
async def process_subtask(self, subtask: Dict[str, Any], 
                         agent_state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
    """
    处理单个子任务的执行
    
    工具链解析:
        1. 解析adv_tool字段
        2. 分割工具名称
        3. 验证工具可用性
        4. 构建执行序列
    
    执行控制:
        - 顺序执行工具链
        - 传递执行上下文
        - 处理工具间数据传递
        - 实现错误重试逻辑
    
    状态跟踪:
        - 更新子任务状态
        - 记录执行进度
        - 收集性能指标
    
    事件发送:
        - 实时发送执行事件
        - 包含详细的执行信息
        - 支持事件过滤
    """
```

##### 工具调用方法
```python
async def _call_tool_with_retry(self, tool_name: str, arguments: Dict[str, Any], 
                               max_retries: int = 3) -> Tuple[bool, Dict[str, Any], str]:
    """
    带重试机制的工具调用
    
    重试策略:
        1. 立即重试: 网络临时错误
        2. 延迟重试: 资源暂时不可用
        3. 参数修正: 参数格式错误
        4. 降级处理: 工具不可用
    
    错误分类:
        - 可重试错误: 网络超时、资源不足
        - 不可重试错误: 参数错误、权限不足
        - 致命错误: 系统故障、配置错误
    
    返回值:
        - success: 是否执行成功
        - result: 执行结果字典
        - error_msg: 错误信息（如果有）
    """
```

### 6. DataLoader 类

**文件**: `utils/data_loader.py`

```python
class DataLoader:
    """
    数据加载器，负责数据库模式信息的获取和管理
    
    功能特性:
    - 异步数据加载
    - 并行表采样
    - 模式信息缓存
    - 错误恢复机制
    
    性能优化:
    - 使用asyncio.gather并行处理
    - 限制采样数据量
    - 智能缓存策略
    """
```

#### 核心方法详解

##### get_database_schema_async 方法
```python
async def get_database_schema_async(self, dataset_id: str) -> Dict[str, Any]:
    """
    异步获取数据库模式信息
    
    执行步骤:
        1. 获取已知表名列表
        2. 构建DLC查询请求
        3. 调用MCP工具获取表结构
        4. 解析表结构响应
        5. 并行采样所有表数据
        6. 构建完整模式信息
    
    并行优化:
        - 使用asyncio.gather并行采样
        - 控制并发数量避免资源耗尽
        - 处理部分失败情况
    
    数据结构:
        返回的字典包含:
        - tables: {table_name: table_info}
        - database_name: 数据库名称
        - datasource_name: 数据源名称
        - engine_name: 引擎名称
        - table_names: 表名列表
        - total_tables: 表总数
    
    错误处理:
        - 网络连接错误
        - 权限验证失败
        - 数据解析错误
        - 部分表访问失败
    """
```

##### sample_table_data_async 方法
```python
async def sample_table_data_async(self, table_name: str, database_name: str,
                                 datasource_name: str, engine_name: str,
                                 sample_size: int = 10) -> Dict[str, Any]:
    """
    异步采样表数据
    
    采样策略:
        - 使用LIMIT子句限制行数
        - 随机采样（如果数据库支持）
        - 保持数据代表性
    
    SQL构建:
        - 动态构建采样SQL
        - 处理特殊字符和关键字
        - 支持不同数据库方言
    
    结果处理:
        - 解析查询结果
        - 格式化数据类型
        - 处理NULL值
        - 生成数据摘要
    
    性能考虑:
        - 限制采样大小
        - 使用流式处理大结果集
        - 缓存采样结果
    """
```

### 7. OpenAIClient 类

**文件**: `utils/openai_client.py`

```python
class OpenAIClient:
    """
    OpenAI API客户端封装
    
    特性:
    - 异步API调用
    - 流式响应支持
    - 函数调用集成
    - 错误重试机制
    
    配置管理:
    - 支持多模型切换
    - 动态参数调整
    - 连接池管理
    """
```

#### API方法详解

##### generate 方法
```python
async def generate(self, messages: List[Dict[str, Any]], 
                  tools: Optional[List[Dict[str, Any]]] = None,
                  tool_choice: Union[str, Dict[str, Any]] = "auto",
                  model: Optional[str] = None) -> ChatCompletionMessage:
    """
    非流式生成响应
    
    函数调用支持:
        - 自动工具选择 (tool_choice="auto")
        - 强制工具调用 (tool_choice={"type": "function", "function": {"name": "tool_name"}})
        - 禁用工具调用 (tool_choice="none")
    
    响应处理:
        - 检查content字段（文本响应）
        - 检查tool_calls字段（函数调用）
        - 处理混合响应类型
    
    错误处理:
        - API调用错误
        - 网络连接错误
        - 响应格式错误
        - 模型不可用错误
    
    性能优化:
        - 连接复用
        - 请求批量化
        - 响应缓存
    """
```

##### generate_stream 方法
```python
async def generate_stream(self, messages: List[Dict[str, Any]], 
                         tools: Optional[List[Dict[str, Any]]] = None,
                         tool_choice: Union[str, Dict[str, Any]] = "auto",
                         model: Optional[str] = None) -> AsyncIterator[ChatCompletionChunk]:
    """
    流式生成响应
    
    流式处理优势:
        - 降低首字节延迟
        - 提供实时反馈
        - 支持长文本生成
        - 改善用户体验
    
    块处理:
        - 增量内容拼接
        - 工具调用状态跟踪
        - 流结束检测
    
    背压处理:
        - 控制生成速度
        - 缓冲区管理
        - 客户端断连检测
    """
```

## 图节点详细说明

### 节点执行模型

```mermaid
graph LR
    subgraph "节点输入"
        State[AgentState]
        Context[执行上下文]
        Dependencies[依赖注入]
    end
    
    subgraph "节点处理"
        Validate[输入验证]
        Process[核心处理]
        Update[状态更新]
    end
    
    subgraph "节点输出"
        NewState[更新后状态]
        Events[事件流]
        Logs[日志记录]
    end
    
    State --> Validate
    Context --> Validate
    Dependencies --> Validate
    
    Validate --> Process
    Process --> Update
    
    Update --> NewState
    Update --> Events
    Update --> Logs
```

### 核心图节点

#### get_user_input_node
```python
async def get_user_input_node(state: AgentState) -> AgentState:
    """
    用户输入节点
    
    职责:
        - 验证用户输入有效性
        - 初始化对话历史
        - 设置会话基础信息
        - 准备后续处理
    
    输入验证:
        - 检查输入是否为空
        - 验证输入长度限制
        - 过滤恶意内容
    
    状态初始化:
        - 设置current_user_input
        - 初始化conversation_history
        - 重置错误标志
    """
```

#### intent_recognition_node
```python
async def intent_recognition_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    """
    意图识别节点
    
    处理流程:
        1. 检查预识别的意图信息
        2. 处理特殊命令（如退出）
        3. 加载数据库模式（如果需要）
        4. 设置澄清标志
        5. 更新意图状态
    
    特殊处理:
        - 退出命令检测
        - 表格选择逻辑
        - 权限验证结果处理
    
    数据库模式加载:
        - 根据意图类型决定是否加载
        - 异步加载避免阻塞
        - 缓存模式信息
    """
```

#### planner_node
```python
async def planner_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    """
    规划节点
    
    规划过程:
        1. 提取意图和实体信息
        2. 准备规划上下文
        3. 选择合适的提示词模板
        4. 调用LLM生成计划
        5. 验证计划结构
        6. 更新状态
    
    上下文准备:
        - 格式化数据库模式
        - 整理RAG示例
        - 处理失败反馈
        - 设置语言偏好
    
    计划验证:
        - JSON格式验证
        - 子任务结构检查
        - 工具组合验证
        - 依赖关系检查
    """
```

#### executor_node
```python
async def executor_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    """
    执行节点
    
    执行管理:
        1. 创建ExecutorAgent实例
        2. 异步初始化执行环境
        3. 启动子任务执行
        4. 收集执行事件
        5. 生成执行总结
    
    事件处理:
        - 实时转发执行事件
        - 过滤和转换事件格式
        - 处理事件背压
    
    结果收集:
        - 收集Jupyter执行事件
        - 聚合执行统计信息
        - 生成性能报告
    
    错误处理:
        - 捕获执行异常
        - 生成错误事件
        - 保持状态一致性
    """
```

## 工具系统详解

### BaseTool 抽象类

```python
class BaseTool:
    """
    工具基类，定义所有工具的通用接口
    
    设计原则:
    - 模板方法模式: 定义执行框架
    - 策略模式: 不同工具实现不同策略
    - 装饰器模式: 添加验证和错误处理
    
    生命周期:
        1. 实例化: 设置工具属性
        2. 验证: 检查输入参数
        3. 执行: 调用具体实现
        4. 后处理: 格式化输出
        5. 清理: 释放资源
    """
    
    def run(self, tool_input: Dict[str, Any]) -> ToolOutputSchema:
        """
        工具执行的公共接口
        
        执行流程:
            1. 输入验证: 使用Pydantic验证参数
            2. 预处理: 准备执行环境
            3. 执行: 调用_execute方法
            4. 后处理: 格式化结果
            5. 异常处理: 捕获和包装异常
        
        验证机制:
            - 使用args_schema进行类型验证
            - 检查必需参数
            - 验证参数范围和格式
        
        错误包装:
            - 统一错误格式
            - 保留原始错误信息
            - 添加上下文信息
        """
```

### 具体工具实现

#### CodeGenTool
```python
class CodeGenTool(BaseTool):
    """
    代码生成工具
    
    功能:
    - 基于任务描述生成Python代码
    - 支持多种编程模式
    - 集成最佳实践模板
    
    生成策略:
    - 模板匹配: 根据任务类型选择模板
    - 上下文感知: 考虑现有变量和环境
    - 错误预防: 生成健壮的代码
    """
```

#### CodeRunEnvTool
```python
class CodeRunEnvTool(BaseTool):
    """
    代码执行环境工具
    
    功能:
    - 在沙箱环境中执行代码
    - 捕获执行输出和错误
    - 管理执行资源
    
    安全措施:
    - 代码沙箱隔离
    - 资源使用限制
    - 危险操作检测
    """
```

## 状态管理详解

### AgentState 状态机

```mermaid
stateDiagram-v2
    [*] --> Initialized: 创建会话
    
    state Initialized {
        [*] --> 设置初始值
        设置初始值 --> 准备处理
        准备处理 --> [*]
    }
    
    Initialized --> IntentRecognition: 开始意图识别
    
    state IntentRecognition {
        [*] --> Layer1
        Layer1 --> Layer2: 需要补充信息
        Layer1 --> Layer3: 信息充足
        Layer2 --> Layer3: 补充完成
        Layer3 --> [*]: 识别完成
    }
    
    IntentRecognition --> Planning: 意图识别完成
    IntentRecognition --> Clarification: 需要澄清
    
    state Planning {
        [*] --> 分析意图
        分析意图 --> 生成计划
        生成计划 --> 验证计划
        验证计划 --> [*]: 计划有效
        验证计划 --> 分析意图: 计划无效
    }
    
    Planning --> Execution: 计划生成成功
    Planning --> Error: 计划生成失败
    
    state Execution {
        [*] --> 初始化执行器
        初始化执行器 --> 执行子任务
        执行子任务 --> 收集结果
        收集结果 --> 执行子任务: 还有子任务
        收集结果 --> [*]: 所有任务完成
    }
    
    Execution --> Completed: 执行成功
    Execution --> Error: 执行失败
    
    Clarification --> IntentRecognition: 澄清完成
    Error --> [*]: 错误处理完成
    Completed --> [*]: 任务完成
```

### 状态转换规则

#### 状态验证
```python
def validate_state_transition(current_state: AgentState, 
                             new_state: AgentState) -> bool:
    """
    验证状态转换的有效性
    
    验证规则:
        1. 必需字段不能为空
        2. 状态转换符合业务逻辑
        3. 数据类型正确
        4. 引用完整性
    
    Args:
        current_state: 当前状态
        new_state: 新状态
        
    Returns:
        bool: 转换是否有效
    """
    # 检查必需字段
    required_fields = ['current_user_input', 'conversation_history']
    for field in required_fields:
        if field not in new_state or new_state[field] is None:
            return False
    
    # 检查状态一致性
    if (new_state.get('identified_intent_name') and 
        not new_state.get('intent_recognizer_slot_state')):
        return False
    
    # 检查计划依赖
    if (new_state.get('current_plan') and 
        not new_state.get('identified_intent_name')):
        return False
    
    return True
```

## 错误处理和恢复机制

### 错误分类体系

```mermaid
graph TD
    Error[系统错误] --> UserError[用户错误]
    Error --> SystemError[系统错误]
    Error --> ExternalError[外部错误]
    
    UserError --> InputError[输入错误]
    UserError --> PermissionError[权限错误]
    UserError --> ValidationError[验证错误]
    
    SystemError --> ConfigError[配置错误]
    SystemError --> ResourceError[资源错误]
    SystemError --> LogicError[逻辑错误]
    
    ExternalError --> NetworkError[网络错误]
    ExternalError --> APIError[API错误]
    ExternalError --> ServiceError[服务错误]
```

### 恢复策略

#### 自动恢复机制
```python
class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.recovery_strategies = {
            'NetworkError': self._handle_network_error,
            'APIError': self._handle_api_error,
            'ResourceError': self._handle_resource_error,
            'ValidationError': self._handle_validation_error
        }
    
    async def recover_from_error(self, error: Exception, 
                               context: Dict[str, Any]) -> Dict[str, Any]:
        """
        从错误中恢复
        
        Args:
            error: 发生的异常
            context: 错误上下文
            
        Returns:
            恢复结果和策略
        """
        error_type = type(error).__name__
        
        if error_type in self.recovery_strategies:
            return await self.recovery_strategies[error_type](error, context)
        else:
            return await self._handle_unknown_error(error, context)
    
    async def _handle_network_error(self, error: Exception, 
                                   context: Dict[str, Any]) -> Dict[str, Any]:
        """处理网络错误"""
        # 实现网络错误恢复逻辑
        return {
            "strategy": "retry_with_backoff",
            "max_retries": 3,
            "base_delay": 1.0
        }
    
    async def _handle_api_error(self, error: Exception, 
                               context: Dict[str, Any]) -> Dict[str, Any]:
        """处理API错误"""
        # 实现API错误恢复逻辑
        return {
            "strategy": "fallback_model",
            "fallback_model": "gpt-3.5-turbo"
        }
```

---

*类和方法参考文档版本: v1.0*  
*最后更新: 2025-08-14*  
*适用对象: 系统维护工程师、新团队成员*

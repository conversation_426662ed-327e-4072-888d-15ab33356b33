# Intellix Data Science Agent 快速参考手册

## 🎯 核心组件速查

### 主要类和职责

| 类名 | 文件位置 | 核心职责 | 关键方法 |
|------|----------|----------|----------|
| **AgentService** | `agent_service.py` | 主服务入口、会话管理 | `stream_chat()`, `request_stop()` |
| **IntentRecognizer** | `agents/intent_recognizer/intent_recognizer.py` | 三层对话管理、意图识别 | `process_query()`, `is_intent_recognition_complete()` |
| **Planner** | `agents/planner/planner.py` | 任务分解、执行规划 | `create_plan()`, `create_plan_sync()` |
| **ExecutorAgent** | `agents/executor/executor.py` | 任务执行协调 | `execute_subtasks()`, `initialize()` |
| **FlexibleReactAgent** | `agents/executor/flexible_react_agent.py` | ReAct模式工具调用 | `process_subtask()`, `create_enhanced_system_prompt()` |
| **DataLoader** | `utils/data_loader.py` | 数据库模式加载 | `get_database_schema_async()`, `sample_table_data_async()` |
| **OpenAIClient** | `utils/openai_client.py` | LLM API封装 | `generate()`, `generate_stream()` |

### 数据结构速查

| 数据结构 | 用途 | 关键字段 |
|----------|------|----------|
| **AgentState** | 图状态管理 | `current_user_input`, `identified_intent_name`, `current_plan` |
| **IntentSlot** | 意图识别状态 | `content`, `dataset` |
| **IntentState** | 对话状态 | `layer`, `conversation_count`, `task` |
| **Plan** | 执行计划 | `task_name`, `subtasks`, `dataset_id` |
| **SubTask** | 子任务定义 | `idx`, `dep`, `desc`, `adv_tool` |
| **ToolOutputSchema** | 工具输出 | `status`, `observation`, `error_message` |

## 🔄 执行流程速查

### 完整执行流程
```
用户输入 → 意图识别 → 任务规划 → 任务执行 → 结果输出
    ↓         ↓         ↓         ↓         ↓
  验证输入   三层对话   子任务分解   工具调用   流式响应
```

### 意图识别三层对话
```
Layer 1: 必要信息识别 (最多3轮)
    ↓
Layer 2: 补充信息收集 (1轮)
    ↓
Layer 3: 完成确认
```

### 工具调用模式
```
解析工具组合 → 顺序执行工具链 → 处理结果 → 调用finish工具
```

## 🛠️ 工具系统速查

### 可用工具列表

| 工具名称 | 功能描述 | 输入参数 | 输出结果 |
|---------|----------|----------|----------|
| **generate_sql__generate_sql** | 自然语言转SQL | `query_description`, `database_name`, `table_names` | SQL语句、推理过程 |
| **nl2code__nl2code** | 自然语言转Python代码 | `user_instruction`, `env_dependencies`, `global_vars` | Python代码、包列表 |
| **jupyter__execute_code** | Jupyter代码执行 | `code`, `required_packages` | 执行结果、输出 |
| **jupyter__load_data_by_sql** | SQL数据加载 | `sql`, `database_name`, `datasource_name` | DataFrame数据 |
| **aisearch__aisearch_retrieve** | 语义检索 | `query`, `top_k` | 相关文档片段 |
| **DLCExecuteQuery** | 数据湖查询 | `sql`, `database`, `datasource`, `engine` | 查询结果 |

### 常用工具组合

| 任务类型 | 工具组合 |
|---------|----------|
| **数据加载** | `generate_sql + jupyter__load_data_by_sql + finish` |
| **数据分析** | `nl2code + jupyter__execute_code + finish` |
| **数据库查询** | `generate_sql + DLCExecuteQuery + finish` |
| **文档查询** | `aisearch_retrieve + finish` |
| **数据预处理** | `load_data_and_join_table + finish` |

## 🎨 事件系统速查

### 事件类型

| 事件类型 | 描述 | 关键字段 |
|---------|------|----------|
| **MessageEvent** | 对话消息 | `content`, `role` |
| **ThinkEvent** | AI思考过程 | `content`, `reasoning_type` |
| **TextEvent** | 纯文本输出 | `content` |
| **TaskListEvent** | 任务列表更新 | `tasks`, `current_task_index` |
| **JupyterEvent** | 代码执行事件 | `cell_id`, `code`, `outputs` |
| **ErrorEvent** | 错误事件 | `error_message`, `error_type` |
| **FinalSummaryEvent** | 最终总结 | `summary`, `task_results` |

### 事件流处理
```python
# 事件监听示例
async for event in agent_service.stream_chat(user_input, session_id):
    if event.type == "MessageEvent":
        print(f"消息: {event.content}")
    elif event.type == "JupyterEvent":
        print(f"代码执行: {event.code}")
    elif event.type == "ErrorEvent":
        print(f"错误: {event.error_message}")
```

## 🔧 配置速查

### 环境变量

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `EXECUTION_MODE` | 执行模式 | `local` | `ray`, `local` |
| `LOG_LEVEL` | 日志级别 | `INFO` | `DEBUG`, `INFO`, `WARNING` |
| `DUMP_TO_NOTEBOOK` | 导出到Notebook | `false` | `true`, `false` |
| `START_AISEARCH_TASK` | 启动AI搜索 | `true` | `true`, `false` |

### 配置文件结构
```yaml
common:
  llm:
    model_name: "DeepSeek-V3-0324"
    base_url: "http://llm-endpoint/v1/"
    api_key: "your-api-key"
    temperature: 0.3
  
  database:
    host: "localhost"
    port: 5432
    database: "intellix_ds"
    username: "postgres"
    password: "password"
```

## 🐛 调试速查

### 常见问题和解决方案

| 问题症状 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 意图识别失败 | 用户输入模糊、提示词不匹配 | 检查输入完整性、验证提示词 |
| 工具调用失败 | 参数错误、服务不可用 | 验证参数格式、检查服务状态 |
| 执行超时 | 代码复杂、数据量大 | 优化代码逻辑、限制数据规模 |
| 内存不足 | 数据集过大、缓存过多 | 使用流式处理、清理缓存 |
| 网络错误 | 外部服务不可达 | 检查网络连接、配置重试 |

### 调试命令

```bash
# 查看服务状态
curl http://localhost:8000/health

# 查看日志
tail -f /var/log/agent.log | grep ERROR

# 检查Redis连接
redis-cli ping

# 检查数据库连接
psql -h localhost -U postgres -d intellix_ds -c "SELECT 1;"

# 检查Jupyter内核
jupyter kernelspec list
```

### 调试代码片段

```python
# 检查会话状态
def debug_session(session_id: str):
    from infra.datascience_agent.agent_service import AgentService
    service = AgentService(mcp_manager)
    state = service.session_states.get(session_id)
    
    if state:
        print(f"Intent: {state.get('identified_intent_name')}")
        print(f"Plan: {bool(state.get('current_plan'))}")
        print(f"Error: {state.get('execution_error')}")
    else:
        print("Session not found")

# 测试工具调用
async def test_tool(tool_name: str, arguments: dict):
    from infra.mcp.manager.mcp_manager import MCPManager
    manager = MCPManager()
    
    try:
        result = await manager.call_tool(tool_name, arguments)
        print(f"Success: {result}")
    except Exception as e:
        print(f"Error: {e}")

# 性能测试
import time
async def perf_test():
    start = time.time()
    # 执行测试代码
    duration = time.time() - start
    print(f"Execution time: {duration:.2f}s")
```

## 📊 性能基准

### 响应时间基准

| 操作类型 | 目标时间 | 实际时间 | 优化建议 |
|---------|----------|----------|----------|
| 意图识别 | < 2秒 | ~1.5秒 | ✅ 达标 |
| 任务规划 | < 3秒 | ~2.8秒 | ✅ 达标 |
| 简单查询 | < 5秒 | ~4.2秒 | ✅ 达标 |
| 复杂分析 | < 30秒 | ~25秒 | ✅ 达标 |
| 模型训练 | < 5分钟 | ~3.5分钟 | ✅ 达标 |

### 资源使用基准

| 资源类型 | 正常范围 | 告警阈值 | 监控方法 |
|---------|----------|----------|----------|
| CPU使用率 | 20-60% | > 80% | Prometheus |
| 内存使用率 | 30-70% | > 85% | Prometheus |
| 并发会话数 | 10-50 | > 100 | 应用指标 |
| 响应时间 | < 5秒 | > 10秒 | APM监控 |

## 🚀 部署速查

### Docker 快速部署
```bash
# 构建镜像
docker build -t intellix-ds-agent .

# 运行容器
docker run -d \
  --name intellix-agent \
  -p 8000:8000 \
  -e EXECUTION_MODE=local \
  -e LOG_LEVEL=INFO \
  intellix-ds-agent

# 检查状态
docker logs intellix-agent
curl http://localhost:8000/health
```

### Kubernetes 快速部署
```bash
# 应用配置
kubectl apply -f k8s/

# 检查状态
kubectl get pods -l app=intellix-ds-agent
kubectl logs -l app=intellix-ds-agent

# 端口转发
kubectl port-forward svc/intellix-ds-agent 8000:8000
```

## 🔍 故障排除速查

### 故障诊断流程

```mermaid
flowchart TD
    Problem[🚨 发现问题] --> Check[🔍 检查症状]
    Check --> Logs[📋 查看日志]
    Logs --> Metrics[📊 检查指标]
    Metrics --> Network[🌐 网络检查]
    Network --> Config[⚙️ 配置验证]
    Config --> Fix[🔧 应用修复]
    Fix --> Test[🧪 验证修复]
    Test --> Monitor[👀 持续监控]
```

### 常用诊断命令

```bash
# 系统健康检查
curl -s http://localhost:8000/health | jq

# 检查服务日志
docker logs intellix-agent --tail 100

# 检查资源使用
docker stats intellix-agent

# 检查网络连接
telnet llm-endpoint 443
telnet database-host 5432

# 检查配置
python -c "from common.config import get_config; print(get_config())"
```

### 紧急恢复步骤

1. **服务重启**
```bash
# Docker环境
docker restart intellix-agent

# Kubernetes环境
kubectl rollout restart deployment/intellix-ds-agent
```

2. **清理缓存**
```bash
# 清理Redis缓存
redis-cli FLUSHDB

# 清理会话状态
python -c "
from infra.datascience_agent.agent_service import AgentService
service.session_states.clear()
"
```

3. **回滚版本**
```bash
# Docker回滚
docker run -d --name intellix-agent-rollback intellix-ds-agent:previous-version

# Kubernetes回滚
kubectl rollout undo deployment/intellix-ds-agent
```

## 📈 监控速查

### 关键指标监控

```bash
# Prometheus查询示例
# 请求成功率
sum(rate(agent_requests_total{status="success"}[5m])) / sum(rate(agent_requests_total[5m])) * 100

# 平均响应时间
histogram_quantile(0.95, rate(agent_request_duration_seconds_bucket[5m]))

# 活跃会话数
agent_active_sessions

# 错误率
sum(rate(agent_requests_total{status="error"}[5m])) / sum(rate(agent_requests_total[5m])) * 100
```

### 告警规则

| 指标 | 阈值 | 持续时间 | 严重级别 |
|------|------|----------|----------|
| 响应时间 > 10秒 | P95 | 2分钟 | 🟡 警告 |
| 错误率 > 5% | 5分钟平均 | 3分钟 | 🟠 严重 |
| 服务不可用 | 健康检查失败 | 1分钟 | 🔴 紧急 |
| 内存使用 > 85% | 持续使用 | 5分钟 | 🟡 警告 |

## 🧪 测试速查

### 测试命令

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_intent_recognizer.py -v

# 运行集成测试
pytest tests/integration/ -v

# 生成测试覆盖率报告
pytest --cov=infra.datascience_agent --cov-report=html

# 性能测试
python tests/performance/load_test.py
```

### 测试数据准备

```python
# 创建测试会话
test_session_id = "test_session_001"
test_user_input = "分析销售数据趋势"

# 模拟MCP管理器
from unittest.mock import MagicMock
mock_mcp_manager = MagicMock()

# 创建测试服务
from infra.datascience_agent.agent_service import AgentService
test_service = AgentService(mock_mcp_manager)
```

## 🔐 安全速查

### 安全检查清单

- [ ] **输入验证**: 所有用户输入经过验证
- [ ] **权限控制**: 基于白名单的权限管理
- [ ] **代码沙箱**: Jupyter执行环境隔离
- [ ] **数据脱敏**: 敏感数据自动处理
- [ ] **审计日志**: 记录关键操作
- [ ] **加密传输**: TLS加密通信
- [ ] **资源限制**: 执行时间和资源限制

### 权限验证代码

```python
def check_advanced_permission(req_context: str) -> bool:
    """检查高级功能权限"""
    if not req_context:
        return False
    
    try:
        context_data = json.loads(req_context)
        whitelist = context_data.get("whitelist", [])
        return "AdvancedFeature" in whitelist
    except:
        return False
```

## 📝 开发备忘录

### 代码提交规范

```bash
# 提交消息格式
git commit -m "feat(intent): 添加新的意图识别类型"
git commit -m "fix(executor): 修复工具调用超时问题"
git commit -m "docs(api): 更新API文档"
git commit -m "test(planner): 添加规划器单元测试"

# 提交类型
# feat: 新功能
# fix: 修复bug
# docs: 文档更新
# test: 测试相关
# refactor: 重构
# perf: 性能优化
# style: 代码格式
```

### 代码审查要点

1. **功能正确性**: 是否实现了预期功能
2. **错误处理**: 是否有完善的异常处理
3. **性能影响**: 是否影响系统性能
4. **安全考虑**: 是否引入安全风险
5. **测试覆盖**: 是否有对应的测试
6. **文档更新**: 是否更新了相关文档

### 发布检查清单

- [ ] **代码审查**: 所有代码通过审查
- [ ] **测试通过**: 单元测试和集成测试全部通过
- [ ] **性能测试**: 性能指标符合要求
- [ ] **安全扫描**: 通过安全漏洞扫描
- [ ] **文档更新**: 更新相关技术文档
- [ ] **变更记录**: 记录版本变更内容
- [ ] **回滚计划**: 准备回滚方案
- [ ] **监控配置**: 配置相应的监控和告警

## 📞 联系信息

### 技术支持

| 角色 | 联系方式 | 负责范围 |
|------|----------|----------|
| **技术负责人** | <EMAIL> | 架构决策、技术方向 |
| **开发团队** | <EMAIL> | 功能开发、bug修复 |
| **运维团队** | <EMAIL> | 部署运维、监控告警 |
| **产品团队** | <EMAIL> | 需求管理、功能规划 |

### 紧急联系

- **24/7技术支持**: +86-xxx-xxxx-xxxx
- **企业微信群**: Intellix-DS-Agent-Emergency
- **邮件列表**: <EMAIL>

---

## 🔗 相关链接

- [📚 完整技术文档](./TECHNICAL_DOCUMENTATION.md)
- [🏗️ 架构设计文档](./ARCHITECTURE_DESIGN.md)
- [📋 API参考文档](./API_REFERENCE.md)
- [🛠️ 开发者指南](./DEVELOPER_GUIDE.md)
- [🔍 类方法详解](./CLASS_METHOD_REFERENCE.md)
- [🌐 项目仓库](https://github.com/company/intellix-ds-agent)
- [📊 监控仪表板](https://monitoring.company.com/intellix-ds-agent)
- [📝 需求管理](https://jira.company.com/projects/INTELLIX)

---

*快速参考手册版本: v1.0*  
*最后更新: 2025-08-14*  
*适用对象: 所有团队成员*

**💡 使用提示**: 
- 🔖 建议将此文档加入浏览器书签，作为日常开发参考
- 📱 可以在移动设备上查看，支持响应式布局
- 🔍 使用 Ctrl+F 快速搜索相关内容
- 📋 定期检查更新，确保信息准确性

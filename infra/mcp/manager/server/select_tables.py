import os
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
from common.logger.logger import logger

from infra.mcp.nl2sql import select_tables
import json
mcp = FastMCP("Select tables Server")


@mcp.tool()
async def select_tables(params: Dict[str, Any]) -> Dict[str, Any]:
    """根据用户提供的自然语言问题，以及数据库，智能选择该数据库下满足问题的表
    参数结构:
            params (Dict[str, Any]): 包含以下键的字典参数:
                - app_id: 应用ID
                - sub_account_uin: 用户ID
                - trace_id: 追踪ID
                - data_engine_name
                - db_info: db 信息->[
                    {
                            "CatalogName": "",
                            "DbName": "customer_db"
                    }
                ]
                - is_sampling: 是否采样（默认False）
                - mcp_url: MCP服务地址
                - type: 引擎类型
                - question: 自然语言问题
                - record_id: 上游定义，每次请求一个都是一个新的 ID，
        返回:
            Dict[str, Any]: 包含以下键的结果字典:
                - selected_tables: 涉及的表列表
    """

    params['sub_account_uin'] = os.getenv('SUB_ACCOUNT_UIN', '')
    params['app_id'] = os.getenv('APP_ID', '')
    params['trace_id'] = os.getenv('TRACE_ID', '')
    params['data_engine_name'] = os.getenv('DATA_ENGINE_NAME', '')
    params['db_info'] = os.getenv('DB_TABLE', '')
    params['is_sampling'] = os.getenv('IS_SAMPLING', "IS_SAMPLING") == "True"
    params['mcp_url'] = json.loads(os.getenv('MCP_URL', '{}'))
    params['type'] = os.getenv('TYPE', '')
    params['record_id'] = os.getenv('RECORD_ID', '')

    try:
        logger.info(f"select_tables params: {params}")
        result = select_tables.select_tables(params)
        logger.info(f"select_tables result: {result}")
    except Exception as e:
        logger.error(f"select_tables error: {str(e)}", exc_info=True)
        return {
            "error": "Table selection failed.",
            "details": str(e)
        }
    return result

if __name__ == "__main__":
    mcp.run(transport="stdio")

# MCP (Model Context Protocol) API 参考文档

## 概述

本文档详细描述了 MCP 系统中所有类、方法和工具的 API 接口，为开发者提供完整的技术参考。

## 核心管理类

### MCPManager

**位置**: `infra/mcp/manager/mcp_manager.py`

```python
class MCPManager:
    """
    MCP工具管理器，提供统一的工具调用接口
    
    Attributes:
        mcp_servers (Dict[str, Dict[str, Any]]): 注册的MCP服务器配置字典
        active_sessions (Dict[str, ClientSession]): 活跃的客户端会话字典
        callback (CallbackDispatcher): 回调事件分发器
        replay_collector (Optional[ReplayDataCollector]): 重放数据收集器
        logger (Logger): 结构化日志记录器
        params (StreamGenerationParams): 流生成参数配置
    """
```

#### 构造方法
```python
def __init__(self):
    """
    初始化MCPManager实例
    
    Initialization Process:
        1. 初始化服务器注册表
        2. 创建会话管理字典
        3. 设置回调分发器
        4. 配置日志记录器
        5. 初始化性能监控
        
    Side Effects:
        - 创建空的服务器注册表
        - 初始化回调分发机制
        - 设置默认配置参数
    """
```

#### 核心方法

##### register_server
```python
def register_server(self, server_name: str, command: str, args: List[str],
                   link: str, url: str = None, 
                   env_input: Optional[Dict[str, str]] = None,
                   mcp_type: str = None):
    """
    注册MCP服务器到管理器
    
    Args:
        server_name (str): 服务器唯一标识符，用于工具调用路由
        command (str): 启动命令，通常是Python解释器路径
        args (List[str]): 命令行参数列表，包含服务器脚本路径
        link (str): 传输协议类型
            - "stdio": 标准输入输出协议
            - "sse": 服务器发送事件协议
            - "http": HTTP RESTful协议
        url (str, optional): 服务器地址，用于远程服务器连接
        env_input (Dict[str, str], optional): 环境变量字典
        mcp_type (str, optional): 服务器类型标识，用于分类管理
        
    Registration Process:
        1. 验证服务器名称唯一性
        2. 根据协议类型配置传输参数
        3. 设置执行环境变量
        4. 创建服务器配置对象
        5. 注册到服务器字典
        6. 记录注册日志
        
    Environment Configuration:
        - EXECUTION_MODE: 执行模式配置
        - IMAGE_TAG: 容器镜像标签
        - CONF_PATH: 配置文件路径
        - LOG_PATH: 日志文件路径
        - WORKING_DIR: 工作目录路径
        - PYTHONPATH: Python模块搜索路径
        
    Error Handling:
        - 重复注册检测
        - 配置参数验证
        - 环境变量检查
    """
```

##### call_tool
```python
async def call_tool(self, tool_name: str, *args, **kwargs) -> Any:
    """
    执行MCP工具调用
    
    Args:
        tool_name (str): 工具名称，格式为 "服务器名__工具名"
        *args: 位置参数（通常不使用）
        **kwargs: 关键字参数，包含工具执行所需的所有参数
        
    Returns:
        Any: 工具执行结果，格式取决于具体工具实现
        
    Tool Name Format:
        - "jupyter__execute_code": Jupyter代码执行工具
        - "generate_sql__generate_sql": SQL生成工具
        - "nl2code__nl2code": 代码生成工具
        - "aisearch__aisearch_retrieve": AI搜索工具
        
    Execution Flow:
        1. 工具名称解析和验证
        2. 服务器查找和验证
        3. 会话获取或创建
        4. 连接建立和初始化
        5. 工具调用请求发送
        6. 结果接收和处理
        7. 回调事件触发
        8. 性能指标记录
        9. 重放数据收集
        
    Session Management:
        - 自动会话创建和复用
        - 连接池管理
        - 会话超时处理
        - 资源清理机制
        
    Error Handling:
        - 服务器不存在: 返回空字典
        - 连接失败: 自动重试机制
        - 工具执行失败: 异常捕获和日志记录
        - 超时处理: 自动断开连接
        
    Performance Optimization:
        - 连接复用减少开销
        - 异步执行提高并发
        - 结果缓存避免重复计算
        - 批量调用优化网络开销
    """
```

##### get_available_tools
```python
async def get_available_tools(self) -> Dict[str, List[str]]:
    """
    获取所有可用工具的列表
    
    Returns:
        Dict[str, List[str]]: 服务器名称到工具列表的映射
        
    Example Return:
        {
            "jupyter": ["execute_code", "load_data_by_sql", "install_packages"],
            "generate_sql": ["generate_sql"],
            "nl2code": ["nl2code"],
            "aisearch": ["aisearch_retrieve"],
            "generate_ad": ["generate_horizontal_ad", "generate_vertical_ad"]
        }
        
    Discovery Process:
        1. 遍历所有注册的服务器
        2. 为每个服务器建立临时会话
        3. 调用list_tools获取工具清单
        4. 聚合所有工具信息
        5. 处理连接失败的服务器
        6. 缓存工具列表信息
        
    Caching Strategy:
        - 工具列表相对稳定，缓存30分钟
        - 服务器重启时自动刷新缓存
        - 支持手动刷新缓存
        
    Error Handling:
        - 单个服务器失败不影响其他服务器
        - 记录失败的服务器信息
        - 返回部分可用的工具列表
    """
```

## MCP 工具 API 详解

### 1. Jupyter 工具集

**位置**: `infra/mcp/jupyter/server.py`

#### execute_code 工具
```python
@mcp.tool()
async def execute_code(code: str, required_packages: List[str] = None,
                      timeout: int = 180) -> Dict[str, Any]:
    """
    在Jupyter内核中执行Python代码
    
    Args:
        code (str): 要执行的Python代码字符串
            - 支持多行代码
            - 支持魔法命令
            - 自动处理缩进
        required_packages (List[str], optional): 需要安装的包列表
            - 格式: ["pandas", "numpy>=1.20.0", "matplotlib"]
            - 支持版本约束
            - 自动依赖解析
        timeout (int, optional): 执行超时时间，默认180秒
            - 防止无限循环
            - 可配置的超时策略
            
    Returns:
        Dict[str, Any]: 执行结果字典
        {
            "outputs": List[Dict[str, Any]],  # 执行输出列表
            "error": bool,                    # 是否有执行错误
            "details": str,                   # 错误详情（如果有）
            "execution_time": float,          # 执行耗时（秒）
            "kernel_id": str,                 # 内核标识符
            "cell_id": str                    # 单元格标识符
        }
        
    Output Structure:
        每个输出项包含：
        - output_type: "stream" | "display_data" | "execute_result" | "error"
        - content: 输出内容
        - metadata: 输出元数据
        
    Execution Environment:
        - 隔离的Python环境
        - 预装常用数据科学包
        - 支持GPU计算
        - 内存和CPU限制
        
    Security Features:
        - 代码静态分析
        - 危险操作检测
        - 文件系统访问控制
        - 网络访问限制
        
    Performance Optimization:
        - 内核复用机制
        - 包缓存策略
        - 并行执行支持
        - 内存使用优化
    """
```

#### load_data_by_sql 工具
```python
@mcp.tool()
async def load_data_by_sql(sql: str, engine_type: str, mcp_url: str,
                          data_engine_name: str, timeout: int = 300) -> Dict[str, Any]:
    """
    通过SQL查询加载数据到DataFrame
    
    Args:
        sql (str): SQL查询语句
            - 支持标准SQL语法
            - 支持引擎特定扩展
            - 自动SQL注入检测
        engine_type (str): 数据引擎类型
            - "dlc": 腾讯云数据湖计算
            - "tchouse_d": 腾讯云数据仓库
            - "spark": Apache Spark
        mcp_url (str): MCP服务URL的JSON字符串
            - 包含服务端点信息
            - 认证凭据配置
        data_engine_name (str): 数据引擎实例名称
        timeout (int, optional): 查询超时时间，默认300秒
        
    Returns:
        Dict[str, Any]: 数据加载结果
        {
            "error": bool,                    # 是否有加载错误
            "outputs": List[Dict[str, Any]],  # Jupyter执行输出
            "length": int,                    # 数据行数
            "column_names": List[str],        # 列名列表
            "column_types": List[str],        # 列类型列表
            "sample_data": List[List[Any]],   # 样本数据
            "load_time": float,               # 加载耗时
            "data_size": int                  # 数据大小（字节）
        }
        
    Data Loading Pipeline:
        1. SQL语句验证和优化
        2. 引擎类型识别和配置
        3. 连接建立和认证
        4. 查询执行和监控
        5. 结果集处理和转换
        6. DataFrame创建和验证
        7. 元数据提取和统计
        
    Engine-Specific Handling:
        DLC引擎:
        - 支持Spark SQL语法
        - 自动分区优化
        - 列式存储优化
        
        TCHouseD引擎:
        - 支持ClickHouse SQL
        - 实时查询优化
        - 压缩传输支持
        
    Error Recovery:
        - 连接重试机制
        - 查询超时处理
        - 内存不足处理
        - 网络异常恢复
    """
```

### 2. NL2SQL 工具集

**位置**: `infra/mcp/manager/server/generate_sql.py`

#### generate_sql 工具
```python
@mcp.tool()
async def generate_sql(engine_type: str = "dlc", 
                      params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    基于自然语言生成SQL查询语句
    
    Args:
        engine_type (str): 数据引擎类型，默认"dlc"
            - "dlc": 数据湖计算引擎
            - "es_sql": Elasticsearch SQL引擎
        params (Dict[str, Any]): 生成参数字典
            - question (str): 自然语言问题
            - app_id (str): 应用标识符
            - sub_account_uin (str): 子账户标识
            - trace_id (str): 请求追踪ID
            - data_engine_name (str): 数据引擎名称
            - db_info (str): 数据库信息JSON字符串
            - is_sampling (bool): 是否启用采样
            - mcp_url (str): MCP服务地址
            - record_id (str): 记录标识符
            
    Returns:
        Dict[str, Any]: SQL生成结果
        {
            "sql": str,                       # 生成的SQL语句
            "reasoning": str,                 # 生成推理过程
            "tables": List[str],              # 涉及的表名列表
            "confidence": float,              # 生成置信度
            "execution_plan": Dict[str, Any], # 执行计划分析
            "optimization_suggestions": List[str]  # 优化建议
        }
        
    Generation Pipeline:
        1. 问题预处理和清洗
        2. 关键词提取和实体识别
        3. 数据库模式分析
        4. 相似示例检索
        5. 多候选SQL生成
        6. 语义相似度评估
        7. 最优SQL选择
        8. SQL验证和优化
        
    Quality Assurance:
        - 语法正确性验证
        - 语义合理性检查
        - 性能影响评估
        - 安全风险检测
        
    Optimization Features:
        - 自动索引建议
        - 查询重写优化
        - 分区裁剪提示
        - 并行执行建议
    """
```

### 3. NL2Code 工具集

**位置**: `infra/mcp/manager/server/nl2code.py`

#### nl2code 工具
```python
@mcp.tool()
async def nl2code(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于自然语言生成可执行Python代码
    
    Args:
        params (Dict[str, Any]): 代码生成参数
            - scenario (str): 数据科学场景类型
                * "general": 通用数据处理
                * "summary_stats": 描述性统计
                * "distribution_analysis": 分布分析
                * "correlation_analysis": 相关性分析
                * "outlier_detection": 异常值检测
                * "data_preprocessing": 数据预处理
                * "feature_engineering": 特征工程
                * "machine_learning": 机器学习
            - user_instruction (str): 用户自然语言指令
            - env_dependencies (List[str]): 环境中已安装的包
            - global_vars (Dict[str, str]): 全局变量字典
            - function_headers (List[str]): 可用函数签名列表
            - previous_actions (List[Tuple[str, str, str]]): 历史执行记录
            - data_type (str): 主要数据对象类型
            - data_schema (str): 数据模式描述
            - model_name (str): LLM模型名称
            
    Returns:
        Dict[str, Any]: 代码生成结果
        {
            "python_code": str,               # 生成的Python代码
            "required_packages": List[str],   # 所需包列表
            "reasoning": str,                 # 生成推理过程
            "detected_scenario": str,         # 检测的场景类型
            "code_complexity": str,           # 代码复杂度评估
            "estimated_runtime": float,       # 预估运行时间
            "memory_requirements": str        # 内存需求评估
        }
        
    Code Generation Process:
        1. 场景检测和分类
        2. 上下文信息整合
        3. 模板选择和定制
        4. LLM代码生成
        5. 代码解析和提取
        6. 语法验证和修复
        7. 包依赖分析
        8. 代码优化和格式化
        
    Context Integration:
        - 现有变量类型分析
        - 数据模式理解
        - 历史操作学习
        - 环境约束考虑
        
    Code Quality Features:
        - 自动错误处理添加
        - 最佳实践应用
        - 性能优化建议
        - 代码注释生成
        
    Package Management:
        - 静态导入分析
        - 版本兼容性检查
        - 依赖冲突检测
        - 安装顺序优化
    """
```

### 4. AI 搜索工具集

**位置**: `infra/mcp/manager/server/aisearch.py`

#### aisearch_retrieve 工具
```python
@mcp.tool()
async def aisearch_retrieve(question: str, 
                           recall_num: Optional[int] = None) -> Dict[str, Any]:
    """
    基于AI的智能文档检索工具
    
    Args:
        question (str): 用户的自然语言查询问题
            - 支持中英文查询
            - 自动查询扩展
            - 同义词识别
        recall_num (int, optional): 召回文档数量
            - 默认值由配置决定
            - 范围: 1-50
            - 影响检索精度和性能
            
    Returns:
        Dict[str, Any]: 检索结果
        {
            "aisearch": List[Dict[str, Any]],  # 检索到的文档列表
            "total_count": int,                # 总文档数量
            "search_time": float,              # 检索耗时
            "rerank_enabled": bool,            # 是否启用重排序
            "query_expansion": List[str]       # 查询扩展词
        }
        
    Document Structure:
        每个文档包含：
        {
            "content": str,                   # 文档内容
            "score": float,                   # 原始相关性评分
            "rerank_score": float,            # 重排序后评分
            "metadata": Dict[str, Any],       # 文档元数据
            "highlight": List[str],           # 高亮片段
            "source": str                     # 文档来源
        }
        
    Retrieval Algorithm:
        1. 查询预处理:
           - 文本清洗和标准化
           - 停用词过滤
           - 查询意图分析
           
        2. 多策略检索:
           - 向量语义检索
           - 关键词精确匹配
           - 混合检索策略
           
        3. 结果优化:
           - 重排序模型优化
           - 相关性阈值过滤
           - 多样性保证
           
        4. 后处理:
           - 结果去重
           - 格式标准化
           - 元数据补充
           
    Performance Features:
        - 查询结果缓存
        - 并行检索执行
        - 增量索引更新
        - 智能预取机制
    """
```

### 5. 广告生成工具集

**位置**: `infra/mcp/manager/server/generate_ad.py`

#### extract_app_info 工具
```python
@mcp.tool()
async def extract_app_info(name: str, score: float, icon_url: str,
                          features: List[str], description: str,
                          ad_type: str = "horizontal") -> Dict[str, Any]:
    """
    从用户输入中提取应用信息并初始化广告生成状态
    
    Args:
        name (str): 应用名称
            - 支持中英文名称
            - 自动格式化处理
        score (float): 应用评分
            - 范围: 0.0-5.0
            - 自动验证有效性
        icon_url (str): 应用图标URL
            - 支持多种图片格式
            - 自动尺寸验证
        features (List[str]): 应用特性列表
            - 核心功能描述
            - 用于广告语生成
        description (str): 应用详细描述
            - 完整功能说明
            - 用于背景生成
        ad_type (str): 广告类型
            - "horizontal": 横版广告 (1280x720)
            - "vertical": 竖版广告 (768x1280)
            
    Returns:
        Dict[str, Any]: 提取的应用信息
        {
            "name": str,                      # 应用名称
            "score": float,                   # 应用评分
            "icon_url": str,                  # 图标URL
            "features": List[str],            # 特性列表
            "description": str,               # 应用描述
            "ad_type": str,                   # 广告类型
            "initialization_status": str      # 初始化状态
        }
        
    State Initialization:
        1. 验证输入参数完整性
        2. 创建广告生成状态对象
        3. 生成初始广告语
        4. 创建背景图生成提示词
        5. 创建使用场景图提示词
        6. 生成初始图片URL
        7. 配置显示元素选项
        
    Content Generation:
        - 广告语生成: 基于应用特性和描述
        - 背景提示词: 根据应用类型和风格
        - 场景提示词: 描述应用使用场景
        
    Validation Rules:
        - 名称长度: 1-50字符
        - 评分范围: 0.0-5.0
        - URL格式验证
        - 特性数量: 1-10个
        - 描述长度: 10-500字符
    """
```

#### generate_horizontal_ad 工具
```python
@mcp.tool()
async def generate_horizontal_ad(dummy: Optional[str] = None) -> Image.Image:
    """
    生成横版广告图像
    
    Args:
        dummy (str, optional): 占位参数，保持接口一致性
        
    Returns:
        Image.Image: 生成的横版广告图像对象
        
    Layout Specifications:
        - 画布尺寸: 1280x720像素 (16:9比例)
        - 背景图层: 全屏背景，支持渐变和纹理
        - 使用场景图: 576x432像素，位置(150,150)
        - 应用图标: 100x100像素，位置(935,150)
        - 应用名称: 位置(935,260)，字体大小30px
        - 评分显示: 位置(915,330)，星级评分
        - 广告语: 位置(930,360)，字体大小18px
        
    Design Elements:
        1. 背景处理:
           - 自动亮度调整
           - 对比度优化
           - 色彩平衡
           
        2. 图像合成:
           - 透明度混合
           - 边缘羽化
           - 阴影效果
           
        3. 文字渲染:
           - 自动换行处理
           - 描边效果
           - 可读性优化
           
        4. 布局适配:
           - 元素自动对齐
           - 间距动态调整
           - 响应式布局
           
    Image Processing:
        - 支持PNG、JPEG、WebP格式
        - 自动格式转换
        - 质量压缩优化
        - 元数据保留
        
    Performance Optimization:
        - 图像缓存机制
        - 并行图像处理
        - 内存使用优化
        - GPU加速支持
    """
```

## 数据结构和模式定义

### 1. 服务器配置模式

```python
class ServerConfig(BaseModel):
    """
    MCP服务器配置数据模型
    
    Attributes:
        params (Optional[StdioServerParameters]): Stdio协议参数
        tools (Optional[List[Tool]]): 可用工具列表
        link (str): 传输协议类型
        url (Optional[str]): 服务器地址
        type (Optional[str]): 服务器类型标识
        health_status (str): 健康状态
        last_health_check (datetime): 最后健康检查时间
        metrics (Dict[str, Any]): 性能指标数据
    """
    
    def validate_config(self) -> bool:
        """
        验证服务器配置的有效性
        
        Validation Rules:
            1. 协议类型必须是支持的类型
            2. Stdio协议必须有command和args
            3. SSE/HTTP协议必须有有效的URL
            4. 环境变量格式正确
            5. 类型标识符符合命名规范
            
        Returns:
            bool: 配置是否有效
        """
```

### 2. 工具调用模式

```python
class ToolCallRequest(BaseModel):
    """
    工具调用请求数据模型
    
    Attributes:
        tool_name (str): 完整的工具名称 (server__tool格式)
        arguments (Dict[str, Any]): 工具参数字典
        timeout (Optional[int]): 调用超时时间
        priority (int): 调用优先级 (1-10)
        retry_config (Optional[RetryConfig]): 重试配置
        callback_config (Optional[CallbackConfig]): 回调配置
    """

class ToolCallResponse(BaseModel):
    """
    工具调用响应数据模型
    
    Attributes:
        status (str): 执行状态 ("success" | "error" | "timeout")
        result (Any): 执行结果数据
        error_message (Optional[str]): 错误信息
        execution_time (float): 执行耗时（毫秒）
        server_name (str): 执行服务器名称
        tool_name (str): 执行工具名称
        metadata (Dict[str, Any]): 执行元数据
    """
```

### 3. 会话管理模式

```python
class ClientSession(BaseModel):
    """
    客户端会话数据模型
    
    Attributes:
        session_id (str): 会话唯一标识符
        server_name (str): 关联的服务器名称
        transport_type (str): 传输协议类型
        connection_status (str): 连接状态
        created_at (datetime): 会话创建时间
        last_activity (datetime): 最后活动时间
        call_count (int): 工具调用次数
        error_count (int): 错误次数
        total_execution_time (float): 总执行时间
    """
    
    def is_expired(self, ttl_seconds: int = 3600) -> bool:
        """
        检查会话是否过期
        
        Args:
            ttl_seconds: 会话生存时间（秒）
            
        Returns:
            bool: 是否过期
        """
    
    def update_activity(self):
        """更新会话活动时间"""
        self.last_activity = datetime.utcnow()
    
    def record_call(self, success: bool, execution_time: float):
        """记录工具调用统计"""
        self.call_count += 1
        if not success:
            self.error_count += 1
        self.total_execution_time += execution_time
        self.update_activity()
```

## 错误处理和异常定义

### MCP 异常体系

```python
class MCPException(Exception):
    """MCP异常基类"""
    def __init__(self, message: str, error_code: str = None, 
                 context: Dict[str, Any] = None):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}

class MCPConnectionError(MCPException):
    """MCP连接错误"""
    pass

class MCPProtocolError(MCPException):
    """MCP协议错误"""
    pass

class MCPToolError(MCPException):
    """MCP工具执行错误"""
    pass

class MCPTimeoutError(MCPException):
    """MCP超时错误"""
    pass

class MCPAuthenticationError(MCPException):
    """MCP认证错误"""
    pass

class MCPAuthorizationError(MCPException):
    """MCP授权错误"""
    pass
```

### 错误处理策略

```python
class MCPErrorHandler:
    """MCP错误处理器"""
    
    def __init__(self):
        self.error_strategies = {
            MCPConnectionError: self._handle_connection_error,
            MCPTimeoutError: self._handle_timeout_error,
            MCPToolError: self._handle_tool_error,
            MCPProtocolError: self._handle_protocol_error
        }
    
    async def handle_error(self, error: Exception, 
                          context: Dict[str, Any]) -> Dict[str, Any]:
        """
        统一错误处理接口
        
        Args:
            error: 发生的异常
            context: 错误上下文信息
            
        Returns:
            Dict[str, Any]: 错误处理结果
            
        Error Handling Strategies:
            1. 连接错误:
               - 自动重连机制
               - 连接池重置
               - 服务器健康检查
               
            2. 超时错误:
               - 增加超时时间重试
               - 任务分解处理
               - 异步执行优化
               
            3. 工具错误:
               - 参数修正重试
               - 降级工具选择
               - 错误信息用户化
               
            4. 协议错误:
               - 消息格式修复
               - 协议版本协商
               - 传输方式切换
        """
```

## 配置管理和部署

### 配置文件结构

```yaml
# mcp_config.yaml
mcp:
  # 全局配置
  global:
    default_timeout: 300
    max_concurrent_calls: 100
    connection_pool_size: 20
    health_check_interval: 30
    
  # 服务器配置
  servers:
    jupyter:
      command: "python3"
      args: ["infra/mcp/jupyter/server.py"]
      link: "stdio"
      type: "jupyter"
      env:
        KERNEL_NAME: "python3"
        TIMEOUT: "300"
        MAX_MEMORY: "4GB"
        
    generate_sql:
      command: "python3"
      args: ["infra/mcp/manager/server/generate_sql.py"]
      link: "stdio"
      type: "nl2sql"
      env:
        MODEL_NAME: "DeepSeek-V3-0324"
        MAX_CANDIDATES: "3"
        
    nl2code:
      command: "python3"
      args: ["infra/mcp/manager/server/nl2code.py"]
      link: "stdio"
      type: "codegen"
      env:
        MODEL_NAME: "DeepSeek-V3-0324"
        CODE_TIMEOUT: "60"
        
    aisearch:
      command: "python3"
      args: ["infra/mcp/manager/server/aisearch.py"]
      link: "stdio"
      type: "aisearch"
      env:
        KNOWLEDGE_BASE_IDS: "[\"kb_001\", \"kb_002\"]"
        RERANK_ENABLED: "true"
        
    dlc:
      link: "sse"
      url: "http://dlc-service:8080/sse"
      type: "dlc"
      
    tchouse_d:
      link: "sse"
      url: "http://tchouse-service:8080/sse"
      type: "tchouse_d"
      
  # 安全配置
  security:
    enable_authentication: true
    enable_authorization: true
    token_expiry: 3600
    max_request_size: "10MB"
    allowed_origins: ["*"]
    
  # 监控配置
  monitoring:
    enable_metrics: true
    metrics_port: 9090
    enable_tracing: true
    trace_sampling_rate: 0.1
    
  # 缓存配置
  cache:
    enable_l1_cache: true
    l1_cache_size: 1000
    l1_cache_ttl: 300
    enable_l2_cache: true
    l2_cache_url: "redis://localhost:6379/1"
    l2_cache_ttl: 3600
```

### 部署架构

```mermaid
graph TB
    subgraph "🌐 负载均衡层"
        NginxLB[Nginx负载均衡器]
        HAProxy[HAProxy]
        HealthCheck[健康检查]
    end
    
    subgraph "🔧 MCP服务层"
        MCPGateway[MCP网关]
        ServiceMesh[服务网格]
        ConfigCenter[配置中心]
    end
    
    subgraph "🛠️ 工具服务集群"
        JupyterPod1[Jupyter Pod 1]
        JupyterPod2[Jupyter Pod 2]
        NL2SQLPod1[NL2SQL Pod 1]
        NL2CodePod1[NL2Code Pod 1]
        AISearchPod1[AI搜索 Pod 1]
    end
    
    subgraph "💾 数据服务层"
        RedisCluster[Redis集群]
        PostgreSQLCluster[PostgreSQL集群]
        ElasticsearchCluster[ES集群]
        MinIOCluster[MinIO对象存储]
    end
    
    subgraph "🖥️ 计算资源层"
        K8sCluster[Kubernetes集群]
        RayCluster[Ray计算集群]
        GPUNodes[GPU计算节点]
    end
    
    NginxLB --> MCPGateway
    HAProxy --> ServiceMesh
    HealthCheck --> ConfigCenter
    
    MCPGateway --> JupyterPod1
    ServiceMesh --> JupyterPod2
    ConfigCenter --> NL2SQLPod1
    
    JupyterPod1 --> RedisCluster
    NL2SQLPod1 --> PostgreSQLCluster
    AISearchPod1 --> ElasticsearchCluster
    
    JupyterPod1 --> RayCluster
    NL2CodePod1 --> GPUNodes
```

---

*MCP API参考文档版本: v1.0*  
*最后更新: 2025-08-14*  
*维护团队: Intellix MCP API Team*

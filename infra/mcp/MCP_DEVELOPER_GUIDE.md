# MCP (Model Context Protocol) 开发者指南

## 快速开始

### 环境准备

```bash
# 1. 克隆项目
git clone <repository-url>
cd intellix-ds-agent

# 2. 创建Python虚拟环境
uv venv .venv --python=3.12
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate  # Windows

# 3. 安装依赖
uv sync

# 4. 配置环境变量
export CONF_PATH=/path/to/config
export LOG_PATH=/path/to/logs
export WORKING_DIR=$(pwd)
```

### 基本使用示例

```python
from infra.mcp.manager.mcp_manager import MCPManager
from infra.mcp.manager.stream_generation_params import StreamGenerationParams

# 1. 创建MCP管理器
manager = MCPManager()

# 2. 配置参数
params = StreamGenerationParams(
    knowledge_base_ids=["kb_001"],
    data_engine_name="test-engine",
    # ... 其他配置
)

# 3. 注册默认服务器
manager.register_default_servers(params)

# 4. 调用工具
result = await manager.call_tool(
    "jupyter__execute_code",
    arguments={"code": "print('Hello, MCP!')"}
)

print(result)
```

## 开发新的 MCP 工具

### 1. 创建工具服务器

#### 步骤1: 创建服务器文件

```python
# infra/mcp/manager/server/my_custom_tool.py
from typing import Dict, Any, Optional
from mcp.server.fastmcp import FastMCP
from common.logger.logger import logger

# 创建MCP服务器实例
mcp = FastMCP("My Custom Tool Server")

@mcp.tool()
async def my_analysis_tool(
    data_input: str,
    analysis_type: str = "basic",
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    自定义分析工具
    
    Args:
        data_input (str): 输入数据标识或路径
        analysis_type (str): 分析类型，默认"basic"
            - "basic": 基础分析
            - "advanced": 高级分析
            - "statistical": 统计分析
        options (Dict[str, Any], optional): 分析选项配置
        
    Returns:
        Dict[str, Any]: 分析结果
        {
            "status": str,                    # 执行状态
            "result": Dict[str, Any],         # 分析结果
            "analysis_type": str,             # 分析类型
            "execution_time": float,          # 执行时间
            "metadata": Dict[str, Any]        # 元数据信息
        }
    """
    try:
        logger.info(f"开始执行自定义分析: {analysis_type}")
        
        # 参数验证
        if not data_input:
            raise ValueError("data_input不能为空")
        
        # 设置默认选项
        options = options or {}
        
        # 执行分析逻辑
        result = await perform_analysis(data_input, analysis_type, options)
        
        return {
            "status": "success",
            "result": result,
            "analysis_type": analysis_type,
            "execution_time": time.time(),
            "metadata": {
                "tool_version": "1.0.0",
                "data_source": data_input
            }
        }
        
    except Exception as e:
        logger.error(f"分析执行失败: {e}")
        return {
            "status": "error",
            "error_message": str(e),
            "analysis_type": analysis_type
        }

# 启动服务器
if __name__ == "__main__":
    mcp.run(transport="stdio")
```

#### 步骤2: 实现核心逻辑

```python
# infra/mcp/my_custom_tool/core.py
import asyncio
from typing import Dict, Any

async def perform_analysis(data_input: str, analysis_type: str, 
                          options: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行自定义分析的核心逻辑
    
    Args:
        data_input: 输入数据
        analysis_type: 分析类型
        options: 分析选项
        
    Returns:
        分析结果字典
    """
    
    # 数据加载
    data = await load_data(data_input)
    
    # 根据分析类型执行不同逻辑
    if analysis_type == "basic":
        result = await basic_analysis(data, options)
    elif analysis_type == "advanced":
        result = await advanced_analysis(data, options)
    elif analysis_type == "statistical":
        result = await statistical_analysis(data, options)
    else:
        raise ValueError(f"不支持的分析类型: {analysis_type}")
    
    return result

async def basic_analysis(data: Any, options: Dict[str, Any]) -> Dict[str, Any]:
    """基础分析实现"""
    # 实现基础分析逻辑
    return {
        "summary": "基础分析完成",
        "data_points": len(data) if hasattr(data, '__len__') else 0,
        "analysis_details": {}
    }

async def load_data(data_input: str) -> Any:
    """数据加载函数"""
    # 实现数据加载逻辑
    # 可以从文件、数据库、API等加载数据
    return {"sample": "data"}
```

#### 步骤3: 注册到MCP管理器

```python
# 在MCPManager中添加新服务器注册
def register_custom_servers(self, params: StreamGenerationParams):
    """注册自定义服务器"""
    
    custom_servers = [
        {
            "name": "my_custom_tool",
            "command": sys.executable,
            "args": [f"{working_dir()}/infra/mcp/manager/server/my_custom_tool.py"],
            "link": "stdio",
            "type": "custom_analysis",
            "env": {
                "ANALYSIS_CONFIG": json.dumps(params.analysis_config),
                "MODEL_NAME": "custom-model-v1",
            },
            "url": None,
        }
    ]
    
    for server_config in custom_servers:
        self.register_server(**server_config)

# 在register_default_servers方法中调用
def register_default_servers(self, params: StreamGenerationParams):
    # ... 现有服务器注册代码 ...
    
    # 注册自定义服务器
    self.register_custom_servers(params)
```

### 2. 工具开发最佳实践

#### 参数验证和类型检查

```python
from pydantic import BaseModel, validator
from typing import List, Optional, Union

class ToolParameters(BaseModel):
    """工具参数验证模型"""
    
    # 必需参数
    primary_input: str
    
    # 可选参数带默认值
    analysis_type: str = "basic"
    timeout: int = 300
    
    # 复杂类型参数
    options: Optional[Dict[str, Any]] = None
    data_sources: List[str] = []
    
    @validator('primary_input')
    def validate_primary_input(cls, v):
        """验证主要输入参数"""
        if not v or not v.strip():
            raise ValueError('primary_input不能为空')
        if len(v) > 10000:
            raise ValueError('primary_input长度不能超过10000字符')
        return v.strip()
    
    @validator('analysis_type')
    def validate_analysis_type(cls, v):
        """验证分析类型"""
        allowed_types = ["basic", "advanced", "statistical", "ml"]
        if v not in allowed_types:
            raise ValueError(f'analysis_type必须是以下之一: {allowed_types}')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        """验证超时时间"""
        if v <= 0 or v > 3600:
            raise ValueError('timeout必须在1-3600秒之间')
        return v

# 在工具中使用参数验证
@mcp.tool()
async def validated_tool(**kwargs) -> Dict[str, Any]:
    """使用参数验证的工具示例"""
    try:
        # 验证参数
        params = ToolParameters(**kwargs)
        
        # 执行工具逻辑
        result = await execute_tool_logic(params)
        
        return {
            "status": "success",
            "result": result
        }
        
    except ValidationError as e:
        return {
            "status": "error",
            "error_type": "validation_error",
            "details": e.errors()
        }
    except Exception as e:
        return {
            "status": "error",
            "error_type": "execution_error",
            "details": str(e)
        }
```

#### 异步编程模式

```python
import asyncio
from typing import List, Coroutine

# ✅ 正确的异步实现
@mcp.tool()
async def parallel_processing_tool(tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """并行处理工具示例"""
    
    async def process_single_task(task: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个任务"""
        # 模拟异步处理
        await asyncio.sleep(0.1)
        return {"task_id": task["id"], "result": f"processed_{task['data']}"}
    
    # 创建并行任务
    coroutines = [process_single_task(task) for task in tasks]
    
    # 并行执行所有任务
    results = await asyncio.gather(*coroutines, return_exceptions=True)
    
    # 处理结果和异常
    successful_results = []
    failed_results = []
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            failed_results.append({
                "task_index": i,
                "error": str(result)
            })
        else:
            successful_results.append(result)
    
    return {
        "status": "completed",
        "successful_count": len(successful_results),
        "failed_count": len(failed_results),
        "results": successful_results,
        "errors": failed_results
    }

# ✅ 使用异步上下文管理器
@mcp.tool()
async def database_tool(query: str) -> Dict[str, Any]:
    """数据库操作工具示例"""
    
    async with get_database_connection() as conn:
        try:
            # 执行查询
            result = await conn.execute(query)
            
            # 处理结果
            data = await result.fetchall()
            
            return {
                "status": "success",
                "data": data,
                "row_count": len(data)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error_message": str(e)
            }
    # 连接自动关闭
```

#### 错误处理和日志记录

```python
import traceback
from common.logger.logger import logger

@mcp.tool()
async def robust_tool(input_data: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """健壮的工具实现示例"""
    
    # 生成唯一的执行ID用于追踪
    execution_id = f"exec_{int(time.time())}_{hash(input_data) % 10000}"
    
    try:
        logger.info(f"开始执行工具 [ID: {execution_id}]", extra={
            "execution_id": execution_id,
            "input_size": len(input_data),
            "config": config
        })
        
        # 输入验证
        if not input_data:
            raise ValueError("输入数据不能为空")
        
        # 执行核心逻辑
        result = await execute_core_logic(input_data, config)
        
        logger.info(f"工具执行成功 [ID: {execution_id}]", extra={
            "execution_id": execution_id,
            "result_size": len(str(result))
        })
        
        return {
            "status": "success",
            "result": result,
            "execution_id": execution_id
        }
        
    except ValueError as e:
        # 参数错误
        logger.warning(f"参数验证失败 [ID: {execution_id}]: {e}", extra={
            "execution_id": execution_id,
            "error_type": "validation_error"
        })
        return {
            "status": "error",
            "error_type": "validation_error",
            "error_message": str(e),
            "execution_id": execution_id
        }
        
    except TimeoutError as e:
        # 超时错误
        logger.error(f"执行超时 [ID: {execution_id}]: {e}", extra={
            "execution_id": execution_id,
            "error_type": "timeout_error"
        })
        return {
            "status": "error",
            "error_type": "timeout_error",
            "error_message": "执行超时，请检查输入数据大小或增加超时时间",
            "execution_id": execution_id
        }
        
    except Exception as e:
        # 未知错误
        logger.error(f"工具执行失败 [ID: {execution_id}]: {e}", extra={
            "execution_id": execution_id,
            "error_type": "execution_error",
            "traceback": traceback.format_exc()
        })
        return {
            "status": "error",
            "error_type": "execution_error",
            "error_message": "内部执行错误，请联系管理员",
            "execution_id": execution_id
        }
```

### 3. 工具测试开发

#### 单元测试示例

```python
# tests/test_my_custom_tool.py
import pytest
from unittest.mock import AsyncMock, patch
from infra.mcp.manager.server.my_custom_tool import my_analysis_tool

@pytest.mark.asyncio
async def test_my_analysis_tool_success():
    """测试工具成功执行"""
    
    # 准备测试数据
    test_input = "sample_data"
    test_options = {"param1": "value1"}
    
    # 模拟核心逻辑
    with patch('infra.mcp.my_custom_tool.core.perform_analysis') as mock_perform:
        mock_perform.return_value = {"analysis_result": "success"}
        
        # 执行工具
        result = await my_analysis_tool(
            data_input=test_input,
            analysis_type="basic",
            options=test_options
        )
        
        # 验证结果
        assert result["status"] == "success"
        assert "result" in result
        assert result["analysis_type"] == "basic"
        
        # 验证调用参数
        mock_perform.assert_called_once_with(test_input, "basic", test_options)

@pytest.mark.asyncio
async def test_my_analysis_tool_validation_error():
    """测试参数验证错误"""
    
    # 测试空输入
    result = await my_analysis_tool(
        data_input="",
        analysis_type="basic"
    )
    
    assert result["status"] == "error"
    assert "validation_error" in result["error_type"]

@pytest.mark.asyncio
async def test_my_analysis_tool_execution_error():
    """测试执行错误"""
    
    with patch('infra.mcp.my_custom_tool.core.perform_analysis') as mock_perform:
        mock_perform.side_effect = Exception("模拟执行错误")
        
        result = await my_analysis_tool(
            data_input="test_data",
            analysis_type="basic"
        )
        
        assert result["status"] == "error"
        assert "execution_error" in result["error_type"]
```

#### 集成测试示例

```python
# tests/integration/test_mcp_integration.py
import pytest
from infra.mcp.manager.mcp_manager import MCPManager
from infra.mcp.manager.stream_generation_params import StreamGenerationParams

@pytest.mark.asyncio
async def test_end_to_end_tool_call():
    """端到端工具调用测试"""
    
    # 创建MCP管理器
    manager = MCPManager()
    
    # 配置测试参数
    params = StreamGenerationParams(
        knowledge_base_ids=["test_kb"],
        data_engine_name="test_engine"
    )
    
    # 注册测试服务器
    manager.register_server(
        server_name="test_server",
        command="python3",
        args=["tests/fixtures/test_server.py"],
        link="stdio",
        mcp_type="test"
    )
    
    # 执行工具调用
    result = await manager.call_tool(
        "test_server__test_tool",
        arguments={"test_param": "test_value"}
    )
    
    # 验证结果
    assert result is not None
    assert isinstance(result, dict)

@pytest.mark.asyncio
async def test_multiple_server_coordination():
    """多服务器协调测试"""
    
    manager = MCPManager()
    
    # 注册多个服务器
    servers = ["jupyter", "generate_sql", "nl2code"]
    for server in servers:
        # 注册服务器逻辑
        pass
    
    # 获取可用工具
    tools = await manager.get_available_tools()
    
    # 验证所有服务器都已注册
    for server in servers:
        assert server in tools
        assert len(tools[server]) > 0
```

## 性能优化指南

### 1. 连接池优化

```python
class OptimizedMCPManager(MCPManager):
    """优化的MCP管理器"""
    
    def __init__(self):
        super().__init__()
        self.connection_pool = {}
        self.pool_size = 10
        self.connection_timeout = 30
    
    async def get_optimized_session(self, server_name: str) -> ClientSession:
        """获取优化的会话连接"""
        
        # 检查连接池
        if server_name in self.connection_pool:
            pool = self.connection_pool[server_name]
            
            # 尝试获取可用连接
            for session in pool:
                if session.is_available():
                    return session
        
        # 创建新连接
        session = await self._create_new_session(server_name)
        
        # 添加到连接池
        if server_name not in self.connection_pool:
            self.connection_pool[server_name] = []
        
        if len(self.connection_pool[server_name]) < self.pool_size:
            self.connection_pool[server_name].append(session)
        
        return session
    
    async def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        
        for server_name, pool in self.connection_pool.items():
            active_sessions = []
            
            for session in pool:
                if current_time - session.last_activity < self.connection_timeout:
                    active_sessions.append(session)
                else:
                    await session.close()
            
            self.connection_pool[server_name] = active_sessions
```

### 2. 缓存策略实现

```python
import hashlib
import json
from typing import Any, Optional

class MCPCache:
    """MCP结果缓存实现"""
    
    def __init__(self):
        self.memory_cache = {}
        self.cache_stats = {"hits": 0, "misses": 0}
        self.max_cache_size = 1000
        self.default_ttl = 3600
    
    def generate_cache_key(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 创建稳定的参数字符串
        sorted_args = json.dumps(arguments, sort_keys=True, ensure_ascii=False)
        
        # 生成哈希
        hash_input = f"{tool_name}:{sorted_args}"
        return hashlib.sha256(hash_input.encode()).hexdigest()[:16]
    
    async def get_cached_result(self, tool_name: str, 
                               arguments: Dict[str, Any]) -> Optional[Any]:
        """获取缓存结果"""
        cache_key = self.generate_cache_key(tool_name, arguments)
        
        if cache_key in self.memory_cache:
            cached_item = self.memory_cache[cache_key]
            
            # 检查TTL
            if time.time() - cached_item["timestamp"] < cached_item["ttl"]:
                self.cache_stats["hits"] += 1
                return cached_item["result"]
            else:
                # 过期删除
                del self.memory_cache[cache_key]
        
        self.cache_stats["misses"] += 1
        return None
    
    async def cache_result(self, tool_name: str, arguments: Dict[str, Any],
                          result: Any, ttl: int = None):
        """缓存结果"""
        cache_key = self.generate_cache_key(tool_name, arguments)
        
        # 检查缓存大小限制
        if len(self.memory_cache) >= self.max_cache_size:
            # 删除最旧的缓存项
            oldest_key = min(self.memory_cache.keys(),
                           key=lambda k: self.memory_cache[k]["timestamp"])
            del self.memory_cache[oldest_key]
        
        # 添加到缓存
        self.memory_cache[cache_key] = {
            "result": result,
            "timestamp": time.time(),
            "ttl": ttl or self.default_ttl
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            "hit_rate": hit_rate,
            "total_requests": total_requests,
            "cache_size": len(self.memory_cache),
            "memory_usage": sum(len(str(item)) for item in self.memory_cache.values())
        }
```

### 3. 监控和指标收集

```python
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义监控指标
TOOL_CALLS_TOTAL = Counter('mcp_tool_calls_total', 
                          'Total MCP tool calls',
                          ['server_name', 'tool_name', 'status'])

TOOL_CALL_DURATION = Histogram('mcp_tool_call_duration_seconds',
                               'MCP tool call duration',
                               ['server_name', 'tool_name'])

ACTIVE_SESSIONS = Gauge('mcp_active_sessions',
                       'Number of active MCP sessions',
                       ['server_name'])

class MonitoredMCPManager(MCPManager):
    """带监控的MCP管理器"""
    
    async def call_tool_with_monitoring(self, tool_name: str, **kwargs):
        """带监控的工具调用"""
        
        # 解析服务器和工具名称
        server_name, actual_tool_name = tool_name.split("__", 1)
        
        # 记录开始时间
        start_time = time.time()
        
        # 增加活跃会话计数
        ACTIVE_SESSIONS.labels(server_name=server_name).inc()
        
        try:
            # 执行工具调用
            result = await super().call_tool(tool_name, **kwargs)
            
            # 记录成功指标
            TOOL_CALLS_TOTAL.labels(
                server_name=server_name,
                tool_name=actual_tool_name,
                status="success"
            ).inc()
            
            return result
            
        except Exception as e:
            # 记录失败指标
            TOOL_CALLS_TOTAL.labels(
                server_name=server_name,
                tool_name=actual_tool_name,
                status="error"
            ).inc()
            
            raise
            
        finally:
            # 记录执行时间
            duration = time.time() - start_time
            TOOL_CALL_DURATION.labels(
                server_name=server_name,
                tool_name=actual_tool_name
            ).observe(duration)
            
            # 减少活跃会话计数
            ACTIVE_SESSIONS.labels(server_name=server_name).dec()
```

## 调试和故障排除

### 1. 调试工具

```python
class MCPDebugger:
    """MCP调试工具"""
    
    def __init__(self, manager: MCPManager):
        self.manager = manager
        self.debug_mode = True
    
    async def debug_tool_call(self, tool_name: str, **kwargs):
        """调试工具调用"""
        
        if self.debug_mode:
            print(f"🔍 调试工具调用: {tool_name}")
            print(f"📥 输入参数: {json.dumps(kwargs, indent=2, ensure_ascii=False)}")
        
        try:
            # 记录调用开始时间
            start_time = time.time()
            
            # 执行工具调用
            result = await self.manager.call_tool(tool_name, **kwargs)
            
            # 记录执行时间
            execution_time = time.time() - start_time
            
            if self.debug_mode:
                print(f"✅ 调用成功，耗时: {execution_time:.2f}秒")
                print(f"📤 输出结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
            
        except Exception as e:
            if self.debug_mode:
                print(f"❌ 调用失败: {e}")
                print(f"📋 错误堆栈: {traceback.format_exc()}")
            
            raise
    
    async def list_server_status(self):
        """列出所有服务器状态"""
        print("🖥️ MCP服务器状态:")
        
        for server_name, config in self.manager.mcp_servers.items():
            try:
                # 尝试获取会话
                session = await self.manager._get_session(server_name)
                status = "🟢 在线" if session else "🔴 离线"
                
                print(f"  {server_name}: {status}")
                print(f"    类型: {config.get('type', 'unknown')}")
                print(f"    协议: {config.get('link', 'unknown')}")
                
                # 获取工具列表
                if session:
                    tools = await session.list_tools()
                    tool_names = [tool.name for tool in tools]
                    print(f"    工具: {', '.join(tool_names)}")
                
            except Exception as e:
                print(f"  {server_name}: 🔴 错误 - {e}")
```

### 2. 常见问题解决

#### 连接问题
```python
async def diagnose_connection_issues(manager: MCPManager, server_name: str):
    """诊断连接问题"""
    
    print(f"🔍 诊断服务器连接: {server_name}")
    
    # 检查服务器是否注册
    if server_name not in manager.mcp_servers:
        print(f"❌ 服务器 {server_name} 未注册")
        return
    
    config = manager.mcp_servers[server_name]
    
    # 检查配置
    print(f"📋 服务器配置:")
    print(f"  协议: {config.get('link')}")
    print(f"  URL: {config.get('url')}")
    print(f"  类型: {config.get('type')}")
    
    # 检查进程状态（对于stdio协议）
    if config.get('link') == 'stdio':
        params = config.get('params')
        if params:
            print(f"  命令: {params.command}")
            print(f"  参数: {params.args}")
            
            # 检查命令是否存在
            import shutil
            if not shutil.which(params.command):
                print(f"❌ 命令不存在: {params.command}")
            else:
                print(f"✅ 命令存在: {params.command}")
    
    # 尝试建立连接
    try:
        session = await manager._get_session(server_name)
        print(f"✅ 连接建立成功")
        
        # 测试工具列表获取
        tools = await session.list_tools()
        print(f"✅ 工具列表获取成功，共 {len(tools)} 个工具")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"📋 错误详情: {traceback.format_exc()}")
```

#### 性能问题诊断
```python
async def diagnose_performance_issues(manager: MCPManager):
    """诊断性能问题"""
    
    print("📊 MCP性能诊断报告")
    
    # 检查活跃会话数
    active_count = len(manager.active_sessions)
    print(f"活跃会话数: {active_count}")
    
    if active_count > 50:
        print("⚠️ 活跃会话数过多，可能影响性能")
    
    # 检查内存使用
    import psutil
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"内存使用: {memory_mb:.1f} MB")
    
    if memory_mb > 1000:
        print("⚠️ 内存使用过高，建议优化")
    
    # 检查工具调用统计
    if hasattr(manager, 'call_stats'):
        stats = manager.call_stats
        print(f"总调用次数: {stats.get('total_calls', 0)}")
        print(f"平均响应时间: {stats.get('avg_response_time', 0):.2f}秒")
        print(f"错误率: {stats.get('error_rate', 0):.2%}")
```

## 部署和运维

### 1. Docker 部署

```dockerfile
# Dockerfile.mcp-server
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY infra/ ./infra/
COPY common/ ./common/

# 设置环境变量
ENV PYTHONPATH=/app
ENV MCP_SERVER_TYPE=jupyter

# 创建非root用户
RUN useradd -m -u 1000 mcpuser
USER mcpuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# 启动命令
CMD ["python", "-m", "infra.mcp.jupyter.server"]
```

### 2. Kubernetes 部署

```yaml
# k8s/mcp-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-jupyter-server
  labels:
    app: mcp-jupyter-server
    component: mcp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcp-jupyter-server
  template:
    metadata:
      labels:
        app: mcp-jupyter-server
    spec:
      containers:
      - name: jupyter-server
        image: mcp-jupyter-server:latest
        env:
        - name: MCP_SERVER_TYPE
          value: "jupyter"
        - name: EXECUTION_MODE
          value: "ray"
        - name: RAY_ADDRESS
          value: "ray://ray-cluster:10001"
        - name: PYTHONPATH
          value: "/app"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        ports:
        - containerPort: 8900
          name: mcp-port
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8900
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: mcp-config
      - name: logs-volume
        emptyDir: {}
      
---
apiVersion: v1
kind: Service
metadata:
  name: mcp-jupyter-service
spec:
  selector:
    app: mcp-jupyter-server
  ports:
  - port: 8900
    targetPort: 8900
    name: mcp-port
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mcp-config
data:
  mcp_config.yaml: |
    mcp:
      global:
        default_timeout: 300
        max_concurrent_calls: 100
      servers:
        jupyter:
          timeout: 300
          max_memory: "4GB"
```

### 3. 监控配置

```yaml
# monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      
    scrape_configs:
    - job_name: 'mcp-servers'
      static_configs:
      - targets: ['mcp-jupyter-service:9090']
      metrics_path: /metrics
      scrape_interval: 10s
      
    rule_files:
    - "mcp_alerts.yml"
    
  mcp_alerts.yml: |
    groups:
    - name: mcp_alerts
      rules:
      - alert: MCPHighErrorRate
        expr: rate(mcp_tool_calls_total{status="error"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MCP工具调用错误率过高"
          description: "服务器 {{ $labels.server_name }} 的错误率超过10%"
          
      - alert: MCPSlowResponse
        expr: histogram_quantile(0.95, rate(mcp_tool_call_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "MCP工具调用响应缓慢"
          description: "95%的调用响应时间超过10秒"
```

## 开发工作流

### 1. 开发环境设置

```bash
#!/bin/bash
# setup_dev_env.sh

echo "🚀 设置MCP开发环境..."

# 创建虚拟环境
uv venv .venv --python=3.12
source .venv/bin/activate

# 安装开发依赖
uv add pytest pytest-asyncio pytest-mock
uv add black isort flake8 mypy
uv add pre-commit

# 安装项目依赖
uv sync

# 设置pre-commit钩子
pre-commit install

# 创建开发配置
cp config/mcp_config.example.yaml config/mcp_config.yaml

echo "✅ 开发环境设置完成"
echo "💡 使用 'python -m pytest tests/' 运行测试"
echo "💡 使用 'python infra/mcp/manager/server/my_tool.py' 启动工具服务器"
```

### 2. 代码质量检查

```bash
#!/bin/bash
# quality_check.sh

echo "🔍 执行代码质量检查..."

# 代码格式化
echo "📝 格式化代码..."
black infra/mcp/
isort infra/mcp/

# 代码风格检查
echo "🎨 检查代码风格..."
flake8 infra/mcp/ --max-line-length=88

# 类型检查
echo "🔍 类型检查..."
mypy infra/mcp/ --ignore-missing-imports

# 运行测试
echo "🧪 运行测试..."
pytest tests/ -v --cov=infra/mcp

echo "✅ 代码质量检查完成"
```

### 3. 发布流程

```bash
#!/bin/bash
# release.sh

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "❌ 请提供版本号: ./release.sh v1.2.3"
    exit 1
fi

echo "🚀 开始发布 MCP $VERSION..."

# 运行测试
echo "🧪 运行完整测试套件..."
pytest tests/ --cov=infra/mcp --cov-report=html

# 构建Docker镜像
echo "🐳 构建Docker镜像..."
docker build -t mcp-server:$VERSION .
docker build -t mcp-server:latest .

# 推送镜像
echo "📤 推送Docker镜像..."
docker push mcp-server:$VERSION
docker push mcp-server:latest

# 更新Kubernetes配置
echo "☸️ 更新Kubernetes部署..."
kubectl set image deployment/mcp-jupyter-server jupyter-server=mcp-server:$VERSION

# 等待部署完成
kubectl rollout status deployment/mcp-jupyter-server

echo "✅ MCP $VERSION 发布完成"
```

---

*MCP开发者指南版本: v1.0*  
*最后更新: 2025-08-14*  
*维护团队: Intellix MCP Development Team*

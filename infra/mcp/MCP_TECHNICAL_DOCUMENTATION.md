# MCP (Model Context Protocol) 系统技术文档

## 概述

MCP (Model Context Protocol) 是 Intellix Data Science Agent 系统的核心工具协议层，负责管理和协调各种专业化工具服务。MCP 系统采用微服务架构，通过标准化的协议接口实现工具的插件化管理和调用。

## 系统架构

### MCP 整体架构图

```mermaid
graph TB
    subgraph "🎯 MCP 管理层"
        MCPManager[MCPManager<br/>工具管理器]
        CallbackDispatcher[CallbackDispatcher<br/>回调分发器]
        ServerRegistry[ServerRegistry<br/>服务注册表]
    end
    
    subgraph "🔌 MCP 服务器层"
        JupyterServer[JupyterServer<br/>代码执行服务]
        NL2SQLServer[NL2SQLServer<br/>SQL生成服务]
        NL2CodeServer[NL2CodeServer<br/>代码生成服务]
        AISearchServer[AISearchServer<br/>智能检索服务]
        GenerateAdServer[GenerateAdServer<br/>广告生成服务]
        DLCServer[DLCServer<br/>数据湖计算服务]
        TCHouseDServer[TCHouseDServer<br/>腾讯云数据仓库服务]
    end
    
    subgraph "🛠️ 核心工具层"
        ExecuteCode[execute_code<br/>代码执行工具]
        LoadDataBySQL[load_data_by_sql<br/>SQL数据加载工具]
        GenerateSQL[generate_sql<br/>SQL生成工具]
        NL2Code[nl2code<br/>代码生成工具]
        AISearch[aisearch_retrieve<br/>智能检索工具]
        GenerateAd[generate_ad<br/>广告生成工具]
    end
    
    subgraph "🔗 协议传输层"
        StdioTransport[Stdio传输<br/>标准输入输出]
        SSETransport[SSE传输<br/>服务器发送事件]
        HTTPTransport[HTTP传输<br/>RESTful API]
    end
    
    subgraph "🏗️ 基础设施层"
        KernelManager[Jupyter内核管理器]
        DatabaseConnector[数据库连接器]
        LLMClient[LLM客户端]
        VectorStore[向量存储]
        ImageGenerator[图像生成器]
    end
    
    MCPManager --> JupyterServer
    MCPManager --> NL2SQLServer
    MCPManager --> NL2CodeServer
    MCPManager --> AISearchServer
    MCPManager --> GenerateAdServer
    MCPManager --> DLCServer
    MCPManager --> TCHouseDServer
    
    JupyterServer --> ExecuteCode
    JupyterServer --> LoadDataBySQL
    NL2SQLServer --> GenerateSQL
    NL2CodeServer --> NL2Code
    AISearchServer --> AISearch
    GenerateAdServer --> GenerateAd
    
    JupyterServer --> StdioTransport
    NL2SQLServer --> SSETransport
    AISearchServer --> HTTPTransport
    
    ExecuteCode --> KernelManager
    LoadDataBySQL --> DatabaseConnector
    GenerateSQL --> LLMClient
    NL2Code --> LLMClient
    AISearch --> VectorStore
    GenerateAd --> ImageGenerator
```

### MCP 协议栈

```mermaid
graph LR
    subgraph "应用层"
        DataScienceAgent[数据科学代理]
        ChatInterface[聊天接口]
    end
    
    subgraph "MCP协议层"
        MCPClient[MCP客户端]
        MCPServer[MCP服务器]
        ProtocolHandler[协议处理器]
    end
    
    subgraph "传输层"
        Stdio[标准I/O]
        SSE[服务器发送事件]
        HTTP[HTTP协议]
    end
    
    subgraph "工具实现层"
        PythonTools[Python工具]
        SQLTools[SQL工具]
        AITools[AI工具]
    end
    
    DataScienceAgent --> MCPClient
    ChatInterface --> MCPClient
    MCPClient <--> ProtocolHandler
    ProtocolHandler <--> MCPServer
    MCPServer --> Stdio
    MCPServer --> SSE
    MCPServer --> HTTP
    MCPServer --> PythonTools
    MCPServer --> SQLTools
    MCPServer --> AITools
```

## 核心组件详解

### 1. MCPManager 类

**文件**: `infra/mcp/manager/mcp_manager.py`

```python
class MCPManager:
    """
    MCP工具管理器，负责管理所有MCP服务器和工具调用
    
    核心职责:
    - 服务器注册和生命周期管理
    - 工具调用的统一接口
    - 会话管理和连接池
    - 回调机制和事件处理
    - 性能监控和指标收集
    
    架构特点:
    - 单例模式: 全局唯一的工具管理器
    - 工厂模式: 动态创建和管理服务器实例
    - 观察者模式: 通过回调机制通知工具调用事件
    - 代理模式: 为底层MCP服务提供统一代理接口
    
    Attributes:
        mcp_servers (Dict[str, Dict[str, Any]]): 注册的MCP服务器字典
        active_sessions (Dict[str, ClientSession]): 活跃的客户端会话
        callback (CallbackDispatcher): 回调分发器
        replay_collector (Optional[ReplayDataCollector]): 重放数据收集器
        logger (Logger): 日志记录器
        params (StreamGenerationParams): 流生成参数
    """
```

#### 核心方法详解

##### register_server 方法
```python
def register_server(self, server_name: str, command: str, args: List[str], 
                   link: str, url: str = None, 
                   env_input: Optional[Dict[str, str]] = None,
                   mcp_type: str = None):
    """
    注册单个MCP服务器
    
    Args:
        server_name (str): 服务器唯一标识符，用于后续工具调用路由
        command (str): 启动命令，通常是Python解释器路径
        args (List[str]): 命令行参数列表，包含服务器脚本路径
        link (str): 协议类型，支持 "stdio", "sse", "http"
        url (str, optional): 服务器地址，用于远程服务器
        env_input (Dict[str, str], optional): 环境变量字典
        mcp_type (str, optional): 服务器类型标识
        
    Implementation Details:
        1. 根据协议类型配置传输参数
        2. 设置执行环境变量
        3. 处理Ray分布式执行模式
        4. 注册服务器到内部字典
        5. 记录注册日志
        
    Protocol Support:
        - stdio: 标准输入输出协议，适用于本地进程
        - sse: 服务器发送事件协议，适用于Web服务
        - http: HTTP协议，适用于RESTful服务
        
    Environment Variables:
        - EXECUTION_MODE: 执行模式 (local/ray)
        - IMAGE_TAG: 镜像标签
        - CONF_PATH: 配置文件路径
        - LOG_PATH: 日志文件路径
        - WORKING_DIR: 工作目录
        - PYTHONPATH: Python路径
    """
```

##### call_tool 方法
```python
async def call_tool(self, tool_name: str, *args, **kwargs):
    """
    执行工具调用的核心方法
    
    Args:
        tool_name (str): 工具名称，格式为 "服务名__工具名"
        *args: 位置参数
        **kwargs: 关键字参数，包含工具执行所需的所有参数
        
    Returns:
        Any: 工具执行结果，格式取决于具体工具
        
    Process Flow:
        1. 解析工具名称，提取服务器名称
        2. 检查服务器是否已注册
        3. 获取或创建客户端会话
        4. 初始化会话连接
        5. 调用指定工具
        6. 处理执行结果
        7. 触发回调事件
        8. 收集性能指标
        9. 记录重放数据
        
    Error Handling:
        - 服务器未注册: 返回空字典
        - 连接失败: 重试机制
        - 工具执行失败: 异常捕获和日志记录
        - 超时处理: 自动断开连接
        
    Performance Monitoring:
        - 调用计数统计
        - 执行时间测量
        - 成功率监控
        - 错误率统计
        
    Session Management:
        - 自动会话创建和复用
        - 连接池管理
        - 会话超时清理
        - 资源释放
    """
```

##### register_default_servers 方法
```python
def register_default_servers(self, params: StreamGenerationParams):
    """
    注册默认的MCP服务器集合
    
    Args:
        params (StreamGenerationParams): 流生成参数，包含配置信息
        
    Registered Servers:
        1. jupyter: Jupyter代码执行服务
        2. generate_sql: SQL生成服务
        3. nl2code: 代码生成服务
        4. aisearch: 智能检索服务
        5. generate_ad: 广告生成服务
        6. dlc: 数据湖计算服务
        7. tchouse_d: 腾讯云数据仓库服务
        8. select_tables: 表选择服务
        
    Configuration:
        - 从配置文件读取服务器参数
        - 设置环境变量和工作目录
        - 配置知识库ID和权限
        - 设置数据引擎连接信息
        
    Environment Setup:
        - 统一的环境变量配置
        - 工作目录设置
        - Python路径配置
        - 日志配置传递
    """
```

### 2. CallbackDispatcher 类

```python
class CallbackDispatcher(MCPCallback):
    """
    回调分发器，管理工具调用的回调机制
    
    功能特性:
    - 支持精确工具名称回调
    - 支持前缀匹配回调
    - 默认回调处理
    - 异步回调执行
    
    Attributes:
        tool_callbacks (Dict[str, MCPCallback]): 精确工具名称回调映射
        prefix_callbacks (Dict[str, MCPCallback]): 前缀匹配回调映射
        default_callback (Optional[MCPCallback]): 默认回调处理器
    """
    
    def register_callback(self, tool_name: str, callback: MCPCallback):
        """注册精确工具名称回调"""
        
    def register_prefix_callback(self, prefix: str, callback: MCPCallback):
        """注册前缀匹配回调"""
        
    async def on_tool_call(self, ctx, tool_name: str, result):
        """
        分发工具调用回调
        
        Dispatch Logic:
            1. 优先匹配精确工具名称
            2. 其次匹配前缀规则
            3. 最后使用默认回调
            
        Use Cases:
            - 工具执行监控
            - 结果后处理
            - 错误通知
            - 性能统计
        """
```

## MCP 服务器详解

### 1. Jupyter 服务器

**文件**: `infra/mcp/jupyter/server.py`

```python
class JupyterServer:
    """
    Jupyter代码执行服务器，提供远程代码执行能力
    
    核心功能:
    - 远程Jupyter内核管理
    - Python代码执行
    - SQL数据加载
    - 包依赖管理
    - 执行结果收集
    
    支持的执行模式:
    - 本地模式: 直接在本地Jupyter内核执行
    - Ray模式: 在Ray集群中分布式执行
    - 容器模式: 在Docker容器中隔离执行
    """
```

#### 核心工具方法

##### execute_code 工具
```python
async def execute_code(**kwargs):
    """
    在远程Jupyter内核中执行Python代码
    
    Args:
        code (str): 要执行的Python代码字符串
        required_packages (List[str], optional): 需要安装的包列表
        timeout (int, optional): 执行超时时间，默认180秒
        
    Returns:
        Dict[str, Any]: 执行结果，包含：
            - outputs: 执行输出列表
            - error: 是否有执行错误 (bool)
            - details: 错误详情（如果有）
            
    Execution Process:
        1. 获取或创建Jupyter内核
        2. 安装所需的Python包
        3. 执行用户代码
        4. 收集执行输出和错误
        5. 格式化返回结果
        
    Kernel Management:
        - 自动内核创建和复用
        - 内核超时管理
        - 资源清理和回收
        - 错误恢复机制
        
    Package Management:
        - 动态包安装
        - 版本冲突检测
        - 安装失败处理
        - 依赖关系解析
        
    Error Handling:
        - 语法错误捕获
        - 运行时异常处理
        - 超时错误处理
        - 资源不足处理
    """
```

##### load_data_by_sql 工具
```python
async def load_data_by_sql(**kwargs):
    """
    执行SQL查询并将结果加载为DataFrame
    
    Args:
        sql (str): 要执行的SQL查询语句
        engine_type (str): 数据引擎类型 (dlc, tchouse_d)
        mcp_url (str): MCP服务URL的JSON字符串
        data_engine_name (str): 数据引擎名称
        timeout (int, optional): 执行超时时间
        
    Returns:
        Dict[str, Any]: 加载结果，包含：
            - error: 是否有错误 (bool)
            - outputs: Jupyter执行输出
            - length: 数据行数
            - column_names: 列名列表
            - column_types: 列类型列表
            
    Data Loading Process:
        1. 解析引擎类型和连接信息
        2. 根据引擎类型选择数据加载器
        3. 生成相应的Python代码
        4. 在Jupyter内核中执行加载代码
        5. 收集DataFrame信息
        
    Supported Engines:
        - DLC (Data Lake Compute): 腾讯云数据湖计算
        - TCHouseD: 腾讯云数据仓库
        - Spark: Apache Spark引擎
        
    Code Generation:
        - DLC Spark模式: 直接Spark SQL
        - DLC MCP模式: 通过MCP调用
        - TCHouseD模式: 通过MCP调用
    """
```

### 2. NL2SQL 服务器

**文件**: `infra/mcp/manager/server/generate_sql.py`

#### generate_sql 工具
```python
async def generate_sql(engine_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据自然语言生成SQL语句
    
    Args:
        engine_type (str): 引擎类型，默认为 "dlc"，日志分析时使用 "es_sql"
        params (Dict[str, Any]): 参数字典，包含：
            - question: 自然语言问题
            - app_id: 应用ID
            - sub_account_uin: 用户ID
            - trace_id: 追踪ID
            - data_engine_name: 数据引擎名称
            - db_info: 数据库信息列表
            - is_sampling: 是否采样
            - mcp_url: MCP服务地址
            - record_id: 记录ID
            
    Returns:
        Dict[str, Any]: 生成结果，包含：
            - sql: 生成的SQL语句
            - reasoning: 推理过程说明
            - tables: 涉及的表列表
            
    Generation Pipeline:
        1. 参数验证和标准化
        2. 关键词提取
        3. 数据库上下文构建
        4. 表结构DDL获取
        5. 示例SQL检索
        6. LLM生成SQL
        7. SQL验证和优化
        8. 结果格式化
        
    Quality Assurance:
        - 多候选SQL生成
        - 语义相似度评估
        - SQL语法验证
        - 执行计划分析
    """
```

### 3. NL2Code 服务器

**文件**: `infra/mcp/manager/server/nl2code.py`

#### nl2code 工具
```python
async def nl2code(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于自然语言生成可执行Python代码
    
    Args:
        params (Dict[str, Any]): 参数字典，包含：
            - user_instruction: 用户指令描述
            - env_dependencies: 环境依赖包列表
            - global_vars: 全局变量字典
            - function_headers: 可用函数签名列表
            - previous_actions: 历史执行记录
            - data_type: 主要数据对象类型
            - data_schema: 数据模式描述
            - model_name: LLM模型名称
            
    Returns:
        Dict[str, Any]: 生成结果，包含：
            - python_code: 可执行Python代码
            - required_packages: 所需包列表
            - reasoning: 生成推理过程
            - detected_scenario: 检测的场景类型
            
    Code Generation Process:
        1. 参数验证和类型检查
        2. 上下文构建和环境分析
        3. 场景检测和模板选择
        4. LLM代码生成
        5. 代码解析和提取
        6. 代码修复和优化
        7. 包依赖分析
        
    Supported Scenarios:
        - general: 通用数据处理
        - summary_stats: 描述性统计
        - distribution_analysis: 分布分析
        - correlation_analysis: 相关性分析
        - outlier_detection: 异常值检测
        - data_preprocessing: 数据预处理
        - feature_engineering: 特征工程
        - machine_learning: 机器学习
        
    Code Quality:
        - 语法正确性验证
        - 最佳实践应用
        - 错误处理集成
        - 性能优化建议
    """
```

### 4. AI 搜索服务器

**文件**: `infra/mcp/manager/server/aisearch.py`

#### aisearch_retrieve 工具
```python
async def aisearch_retrieve(question: str, recall_num: Optional[int] = None) -> Dict[str, Any]:
    """
    知识库检索工具，基于用户查询进行智能文档检索
    
    Args:
        question (str): 用户的自然语言查询问题
        recall_num (int, optional): 召回文档数量，默认由配置决定
        
    Returns:
        Dict[str, Any]: 检索结果，包含：
            - aisearch: 检索到的文档列表
            - error: 错误信息（如果有）
            - details: 详细错误描述
            
    Document Structure:
        每个文档包含：
        - content: 文档内容
        - score: 相关性评分
        - rerank_score: 重排序后的评分
        - metadata: 文档元数据
        
    Retrieval Pipeline:
        1. 查询文本向量化
        2. 向量相似度搜索
        3. 混合搜索策略
        4. 结果重排序
        5. 相关性过滤
        6. 结果格式化
        
    Search Strategies:
        - 向量搜索: 基于语义相似度
        - 关键词搜索: 基于文本匹配
        - 混合搜索: 结合向量和关键词
        
    Reranking:
        - 使用专门的重排序模型
        - 提高结果相关性
        - 支持配置开关
    """
```

### 5. 广告生成服务器

**文件**: `infra/mcp/manager/server/generate_ad.py`

#### 核心工具方法

##### generate_horizontal_ad 工具
```python
async def generate_horizontal_ad(dummy: Optional[str] = None) -> Image.Image:
    """
    生成横版广告图
    
    Returns:
        Image.Image: 生成的横版广告图像
        
    Generation Process:
        1. 获取当前应用状态
        2. 设置广告类型为横版
        3. 生成背景图片（如果缺失）
        4. 生成使用场景图片（如果缺失）
        5. 应用横版布局设计
        6. 保存生成的广告图
        
    Layout Design:
        - 画布尺寸: 1280x720
        - 背景图片: 全屏背景
        - 使用场景图: 576x432，位置(150,150)
        - 应用图标: 100x100，位置(935,150)
        - 应用名称: 位置(935,260)，字体大小30
        - 评分显示: 位置(915,330)
        - 广告语: 位置(930,360)，字体大小18
    """
```

##### generate_vertical_ad 工具
```python
async def generate_vertical_ad(dummy: Optional[str] = None) -> Image.Image:
    """
    生成竖版广告图
    
    Returns:
        Image.Image: 生成的竖版广告图像
        
    Layout Design:
        - 画布尺寸: 768x1280
        - 背景图片: 全屏背景
        - 使用场景图: 1152x864，居中显示
        - 应用图标: 100x100，居中显示
        - 文本元素: 垂直排列布局
        - 半透明蒙版: 提高文字可读性
    """
```

## 工具系统架构

### 工具分类体系

```mermaid
graph TB
    subgraph "🔧 基础工具类"
        BaseTool[BaseTool<br/>工具基类]
        ToolSchema[工具模式定义]
        ToolRegistry[工具注册表]
    end
    
    subgraph "💻 代码执行工具"
        ExecuteCode[execute_code<br/>Python代码执行]
        LoadData[load_data_by_sql<br/>SQL数据加载]
        InstallPackages[install_packages<br/>包管理]
    end
    
    subgraph "🗄️ 数据处理工具"
        GenerateSQL[generate_sql<br/>SQL生成]
        SelectTables[select_tables<br/>表选择]
        DLCQuery[DLC查询工具]
        TCHouseDQuery[TCHouseD查询工具]
    end
    
    subgraph "🧠 AI智能工具"
        NL2Code[nl2code<br/>代码生成]
        AISearch[aisearch_retrieve<br/>智能检索]
        GenerateAd[generate_ad<br/>广告生成]
    end
    
    subgraph "🔍 辅助工具"
        ExtractKeywords[extract_keywords<br/>关键词提取]
        ValidateSQL[validate_sql<br/>SQL验证]
        FormatResult[format_result<br/>结果格式化]
    end
    
    BaseTool --> ExecuteCode
    BaseTool --> LoadData
    BaseTool --> GenerateSQL
    BaseTool --> NL2Code
    BaseTool --> AISearch
    BaseTool --> GenerateAd
    
    GenerateSQL --> SelectTables
    GenerateSQL --> ExtractKeywords
    GenerateSQL --> ValidateSQL
    
    NL2Code --> FormatResult
    LoadData --> DLCQuery
    LoadData --> TCHouseDQuery
```

### 工具调用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant MCPManager as MCP管理器
    participant Server as MCP服务器
    participant Tool as 具体工具
    participant Resource as 外部资源
    
    Client->>MCPManager: 调用工具
    MCPManager->>MCPManager: 解析工具名称
    MCPManager->>Server: 获取/创建会话
    Server->>Server: 初始化连接
    MCPManager->>Server: 发送工具调用请求
    Server->>Tool: 执行具体工具
    Tool->>Resource: 访问外部资源
    Resource-->>Tool: 返回资源数据
    Tool->>Tool: 处理和格式化结果
    Tool-->>Server: 返回执行结果
    Server-->>MCPManager: 返回标准化结果
    MCPManager->>MCPManager: 触发回调事件
    MCPManager-->>Client: 返回最终结果
```

## 数据流和协议设计

### MCP 协议消息格式

```mermaid
classDiagram
    class MCPMessage {
        +id: str
        +method: str
        +params: Dict[str, Any]
        +result: Optional[Any]
        +error: Optional[MCPError]
    }
    
    class ToolCall {
        +name: str
        +arguments: Dict[str, Any]
    }
    
    class ToolResult {
        +content: List[Content]
        +isError: bool
    }
    
    class Content {
        +type: str
        +text: Optional[str]
        +data: Optional[bytes]
    }
    
    MCPMessage --> ToolCall
    MCPMessage --> ToolResult
    ToolResult --> Content
```

### 传输协议支持

#### 1. Stdio 传输
```python
# 标准输入输出传输，适用于本地进程通信
async def run_stdio():
    """启动stdio服务器"""
    from mcp.server.stdio import stdio_server
    async with stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream, 
                        server.create_initialization_options())
```

#### 2. SSE 传输
```python
# 服务器发送事件传输，适用于Web应用
def run_sse(port: int = 8900):
    """启动SSE服务器"""
    from mcp.server.sse import SseServerTransport
    sse = SseServerTransport("/messages/")
    
    async def handle_sse(request):
        async with sse.connect_sse(request.scope, request.receive, request._send) as streams:
            await server.run(streams[0], streams[1], 
                           server.create_initialization_options())
```

#### 3. HTTP 传输
```python
# HTTP RESTful传输，适用于微服务架构
async def mcp_call_tool(url: str, tool_name: str, arguments: dict, 
                       timeout: float = 5, sse_read_timeout: float = 300):
    """
    通过HTTP执行MCP工具调用
    
    Args:
        url: MCP服务URL
        tool_name: 工具名称
        arguments: 工具参数
        timeout: HTTP请求超时
        sse_read_timeout: SSE读取超时
        
    Process:
        1. 建立SSE连接
        2. 初始化MCP会话
        3. 发送工具调用请求
        4. 接收执行结果
        5. 处理异常和重试
    """
```

## 核心工具实现详解

### 1. SQL 生成核心 (NL2SQL)

**文件**: `infra/mcp/nl2sql/core/generate_pipeline.py`

```python
class SQLGenerator:
    """
    SQL生成器，实现自然语言到SQL的转换
    
    核心算法:
    - 关键词提取和语义分析
    - 数据库模式理解
    - 多候选SQL生成
    - 语义相似度选择
    - SQL优化和验证
    
    Attributes:
        question (str): 自然语言问题
        metadata (Metadata): 元数据信息
        database_info (DatabaseInfo): 数据库配置信息
    """
    
    def generate(self) -> SQLGenerateData:
        """
        生成SQL的主要流程
        
        Process:
            1. 提取问题关键词
            2. 构建数据库上下文
            3. 获取表结构DDL
            4. 检索相似示例
            5. 生成候选SQL
            6. 选择最优SQL
            7. 验证和优化
            
        Returns:
            SQLGenerateData: 包含SQL、推理过程和涉及表的结果对象
        """
```

### 2. 代码生成核心 (NL2Code)

**文件**: `infra/mcp/codegen/nl2code/core.py`

```python
def nl2code_w_pkgs_by_scenario(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于场景的代码生成，支持包依赖分析
    
    Args:
        params: 包含用户指令、环境信息、数据模式等的参数字典
        
    Returns:
        Dict[str, Any]: 包含生成代码、所需包和检测场景的结果
        
    Scenario Detection:
        - 自动检测数据科学场景类型
        - 选择对应的代码模板
        - 应用场景特定的优化
        
    Context Building:
        - 分析现有全局变量
        - 理解数据模式结构
        - 考虑历史执行记录
        - 整合环境依赖信息
        
    Code Generation:
        - 使用场景特定的提示词
        - 生成符合最佳实践的代码
        - 自动添加错误处理
        - 优化代码可读性
        
    Package Analysis:
        - 静态代码分析提取导入
        - 验证包的可用性
        - 处理版本兼容性
        - 生成安装建议
    """
```

### 3. AI 搜索核心

**文件**: `infra/mcp/aisearch/core.py`

```python
def retrieve_by_score(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于评分的智能文档检索
    
    Args:
        params: 包含查询问题、应用ID、知识库ID等的参数字典
        
    Returns:
        Dict[str, Any]: 包含检索结果的字典
        
    Retrieval Pipeline:
        1. 查询预处理和清洗
        2. 文本向量化编码
        3. 向量相似度搜索
        4. 关键词匹配搜索
        5. 混合搜索结果合并
        6. 重排序模型优化
        7. 相关性阈值过滤
        8. 结果格式化输出
        
    Search Configuration:
        - search_type: 搜索类型 (0-混合, 1-向量, 2-关键词)
        - rerank_status: 重排序开关 (0-开启, 1-关闭)
        - recall_num: 召回数量限制
        - score_threshold: 相关性阈值
        
    Quality Enhancement:
        - 多阶段检索策略
        - 语义理解增强
        - 上下文相关性分析
        - 结果去重和聚合
    """
```

## 数据管理和存储

### 数据加载器架构

```mermaid
graph TB
    subgraph "🔌 数据连接层"
        DLCConnector[DLC连接器]
        TCHouseDConnector[TCHouseD连接器]
        SparkConnector[Spark连接器]
        PostgreSQLConnector[PostgreSQL连接器]
    end
    
    subgraph "📊 数据加载器"
        DLCLoader[DLC数据加载器]
        TCHouseDLoader[TCHouseD数据加载器]
        SparkLoader[Spark数据加载器]
        MCPLoader[MCP数据加载器]
    end
    
    subgraph "🔄 数据处理层"
        DataTransformer[数据转换器]
        SchemaValidator[模式验证器]
        TypeConverter[类型转换器]
        DataSampler[数据采样器]
    end
    
    subgraph "💾 数据缓存层"
        MemoryCache[内存缓存]
        RedisCache[Redis缓存]
        FileCache[文件缓存]
    end
    
    DLCConnector --> DLCLoader
    TCHouseDConnector --> TCHouseDLoader
    SparkConnector --> SparkLoader
    
    DLCLoader --> DataTransformer
    TCHouseDLoader --> DataTransformer
    SparkLoader --> DataTransformer
    MCPLoader --> DataTransformer
    
    DataTransformer --> SchemaValidator
    SchemaValidator --> TypeConverter
    TypeConverter --> DataSampler
    
    DataSampler --> MemoryCache
    DataSampler --> RedisCache
    DataSampler --> FileCache
```

### 数据加载器实现

**文件**: `infra/mcp/jupyter/data_loaders.py`

#### DLC 数据加载器
```python
def dlc_spark_data_loader(sql: str) -> str:
    """
    DLC Spark数据加载器，生成直接的Spark SQL执行代码
    
    Args:
        sql (str): SQL查询语句
        
    Returns:
        str: 可执行的Python代码
        
    Generated Code Pattern:
        ```python
        # 执行SQL查询并加载数据
        df = spark.sql('''
        {sql}
        ''')
        
        # 转换为Pandas DataFrame
        df = df.toPandas()
        
        # 输出数据信息
        print("data loaded")
        print(f"data shape: {df.shape}")
        print(f"data types: {df.dtypes.to_dict()}")
        print(f"data columns: {df.columns.tolist()}")
        print("data example:")
        df.head()
        ```
    """
```

#### MCP 数据加载器
```python
def dlc_mcp_data_loader(sql: str, mcp_url: str, data_engine_name: str) -> str:
    """
    DLC MCP数据加载器，通过MCP协议加载数据
    
    Args:
        sql (str): SQL查询语句
        mcp_url (str): MCP服务URL
        data_engine_name (str): 数据引擎名称
        
    Returns:
        str: 可执行的Python代码
        
    Generated Code Pattern:
        ```python
        import mcp
        import pandas as pd
        
        # 通过MCP调用DLC查询
        result = await mcp.call_tool("DLCExecuteQuery", {
            "SparkSQL": sql,
            "DataEngineName": data_engine_name,
            # ... 其他参数
        })
        
        # 解析结果为DataFrame
        df = pd.DataFrame(result['data'], columns=result['columns'])
        
        # 输出数据信息
        print("data loaded")
        # ... 数据信息输出
        ```
    """
```

## 错误处理和恢复机制

### MCP 错误分类

```mermaid
graph TD
    MCPError[MCP错误] --> ConnectionError[连接错误]
    MCPError --> ProtocolError[协议错误]
    MCPError --> ToolError[工具错误]
    MCPError --> TimeoutError[超时错误]
    
    ConnectionError --> NetworkFailure[网络故障]
    ConnectionError --> ServerUnavailable[服务器不可用]
    ConnectionError --> AuthenticationFailure[认证失败]
    
    ProtocolError --> InvalidMessage[消息格式错误]
    ProtocolError --> UnsupportedVersion[版本不兼容]
    ProtocolError --> SerializationError[序列化错误]
    
    ToolError --> ToolNotFound[工具不存在]
    ToolError --> InvalidArguments[参数错误]
    ToolError --> ExecutionFailure[执行失败]
    
    TimeoutError --> RequestTimeout[请求超时]
    TimeoutError --> ExecutionTimeout[执行超时]
    TimeoutError --> ConnectionTimeout[连接超时]
```

### 错误恢复策略

```python
class MCPErrorHandler:
    """MCP错误处理器"""
    
    async def handle_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理MCP错误并尝试恢复
        
        Args:
            error: 发生的异常
            context: 错误上下文信息
            
        Returns:
            恢复结果和策略
            
        Recovery Strategies:
            1. 连接错误 → 重新建立连接
            2. 超时错误 → 增加超时时间重试
            3. 工具错误 → 参数修正或降级处理
            4. 协议错误 → 消息格式修复
        """
        
        if isinstance(error, ConnectionError):
            return await self._handle_connection_error(error, context)
        elif isinstance(error, TimeoutError):
            return await self._handle_timeout_error(error, context)
        elif isinstance(error, ToolError):
            return await self._handle_tool_error(error, context)
        else:
            return await self._handle_unknown_error(error, context)
```

## 性能优化和监控

### 性能监控指标

```mermaid
graph TB
    subgraph "📊 业务指标"
        ToolCallCount[工具调用次数]
        ToolSuccessRate[工具成功率]
        AvgExecutionTime[平均执行时间]
        ConcurrentSessions[并发会话数]
    end
    
    subgraph "🖥️ 系统指标"
        CPUUsage[CPU使用率]
        MemoryUsage[内存使用率]
        NetworkIO[网络I/O]
        DiskIO[磁盘I/O]
    end
    
    subgraph "🔧 服务指标"
        ServerAvailability[服务器可用性]
        ConnectionPoolSize[连接池大小]
        QueueLength[请求队列长度]
        ErrorRate[错误率]
    end
    
    subgraph "📈 性能指标"
        ResponseTime[响应时间分布]
        Throughput[吞吐量]
        Latency[延迟统计]
        ResourceUtilization[资源利用率]
    end
```

### 性能优化策略

#### 1. 连接池管理
```python
class MCPConnectionPool:
    """MCP连接池管理器"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.active_connections = {}
        self.connection_queue = asyncio.Queue()
        self.connection_lock = asyncio.Lock()
    
    async def get_connection(self, server_name: str) -> ClientSession:
        """获取连接，支持复用和池化"""
        async with self.connection_lock:
            if server_name in self.active_connections:
                return self.active_connections[server_name]
            
            if len(self.active_connections) >= self.max_connections:
                # 等待连接释放
                await self.connection_queue.get()
            
            # 创建新连接
            connection = await self._create_connection(server_name)
            self.active_connections[server_name] = connection
            return connection
    
    async def release_connection(self, server_name: str):
        """释放连接"""
        if server_name in self.active_connections:
            del self.active_connections[server_name]
            self.connection_queue.put_nowait(None)
```

#### 2. 缓存机制
```python
class MCPResultCache:
    """MCP结果缓存管理器"""
    
    def __init__(self):
        self.memory_cache = {}
        self.cache_ttl = 3600  # 1小时
        self.max_cache_size = 1000
    
    async def get_cached_result(self, tool_name: str, 
                               arguments: Dict[str, Any]) -> Optional[Any]:
        """获取缓存的工具调用结果"""
        cache_key = self._generate_cache_key(tool_name, arguments)
        
        if cache_key in self.memory_cache:
            result, timestamp = self.memory_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return result
            else:
                del self.memory_cache[cache_key]
        
        return None
    
    async def cache_result(self, tool_name: str, arguments: Dict[str, Any], 
                          result: Any):
        """缓存工具调用结果"""
        if len(self.memory_cache) >= self.max_cache_size:
            # 清理最旧的缓存项
            oldest_key = min(self.memory_cache.keys(), 
                           key=lambda k: self.memory_cache[k][1])
            del self.memory_cache[oldest_key]
        
        cache_key = self._generate_cache_key(tool_name, arguments)
        self.memory_cache[cache_key] = (result, time.time())
```

## 安全和权限控制

### MCP 安全架构

```mermaid
graph TB
    subgraph "🔐 认证层"
        TokenAuth[Token认证]
        CertAuth[证书认证]
        APIKeyAuth[API Key认证]
    end
    
    subgraph "🛡️ 授权层"
        RBAC[基于角色的访问控制]
        ToolPermission[工具权限管理]
        ResourceQuota[资源配额控制]
    end
    
    subgraph "🔒 安全策略层"
        InputValidation[输入验证]
        OutputSanitization[输出清理]
        CodeSandbox[代码沙箱]
        ResourceLimit[资源限制]
    end
    
    subgraph "📋 审计层"
        AccessLog[访问日志]
        OperationAudit[操作审计]
        SecurityAlert[安全告警]
    end
    
    TokenAuth --> RBAC
    CertAuth --> RBAC
    APIKeyAuth --> RBAC
    
    RBAC --> ToolPermission
    RBAC --> ResourceQuota
    
    ToolPermission --> InputValidation
    ResourceQuota --> OutputSanitization
    
    InputValidation --> CodeSandbox
    OutputSanitization --> ResourceLimit
    
    CodeSandbox --> AccessLog
    ResourceLimit --> OperationAudit
    AccessLog --> SecurityAlert
```

### 权限控制实现

```python
class MCPSecurityManager:
    """MCP安全管理器"""
    
    def __init__(self):
        self.tool_permissions = {}
        self.user_roles = {}
        self.resource_quotas = {}
    
    async def check_tool_permission(self, user_id: str, tool_name: str) -> bool:
        """检查工具调用权限"""
        user_role = self.user_roles.get(user_id, "guest")
        allowed_tools = self.tool_permissions.get(user_role, [])
        
        # 检查精确匹配
        if tool_name in allowed_tools:
            return True
        
        # 检查前缀匹配
        for allowed_tool in allowed_tools:
            if allowed_tool.endswith("*") and tool_name.startswith(allowed_tool[:-1]):
                return True
        
        return False
    
    async def validate_input(self, tool_name: str, arguments: Dict[str, Any]) -> bool:
        """验证工具输入参数"""
        # 检查SQL注入
        if "sql" in arguments:
            if self._detect_sql_injection(arguments["sql"]):
                return False
        
        # 检查代码注入
        if "code" in arguments:
            if self._detect_code_injection(arguments["code"]):
                return False
        
        # 检查文件路径遍历
        for key, value in arguments.items():
            if isinstance(value, str) and self._detect_path_traversal(value):
                return False
        
        return True
```

## 配置和部署

### MCP 服务器配置

```yaml
# mcp_config.yaml
mcp_servers:
  jupyter:
    command: "python3"
    args: ["infra/mcp/jupyter/server.py"]
    link: "stdio"
    type: "jupyter"
    env:
      KERNEL_NAME: "python3"
      TIMEOUT: "300"
      
  generate_sql:
    command: "python3"
    args: ["infra/mcp/manager/server/generate_sql.py"]
    link: "stdio"
    type: "nl2sql"
    env:
      MODEL_NAME: "DeepSeek-V3-0324"
      
  nl2code:
    command: "python3"
    args: ["infra/mcp/manager/server/nl2code.py"]
    link: "stdio"
    type: "codegen"
    
  aisearch:
    command: "python3"
    args: ["infra/mcp/manager/server/aisearch.py"]
    link: "stdio"
    type: "aisearch"
    env:
      KNOWLEDGE_BASE_IDS: "[\"kb_001\", \"kb_002\"]"
      
  dlc:
    link: "sse"
    url: "http://dlc-service:8080/sse"
    type: "dlc"
    
  tchouse_d:
    link: "sse"
    url: "http://tchouse-service:8080/sse"
    type: "tchouse_d"
```

### Docker 部署配置

```dockerfile
# MCP服务器通用Dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制MCP代码
COPY infra/mcp/ ./infra/mcp/
COPY common/ ./common/

# 设置环境变量
ENV PYTHONPATH=/app
ENV MCP_SERVER_TYPE=jupyter

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s \
    CMD python -c "import sys; sys.exit(0)"

# 启动命令
CMD ["python", "-m", "infra.mcp.jupyter.server"]
```

### Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-jupyter-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcp-jupyter-server
  template:
    metadata:
      labels:
        app: mcp-jupyter-server
    spec:
      containers:
      - name: jupyter-server
        image: mcp-jupyter-server:latest
        env:
        - name: MCP_SERVER_TYPE
          value: "jupyter"
        - name: EXECUTION_MODE
          value: "ray"
        - name: RAY_ADDRESS
          value: "ray://ray-cluster:10001"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        ports:
        - containerPort: 8900
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: mcp-jupyter-service
spec:
  selector:
    app: mcp-jupyter-server
  ports:
  - port: 8900
    targetPort: 8900
  type: ClusterIP
```

## 扩展开发指南

### 添加新的 MCP 工具

#### 1. 定义工具接口
```python
# 在新的服务器文件中定义工具
from mcp.server.fastmcp import FastMCP
from typing import Dict, Any

mcp = FastMCP("Custom Tool Server")

@mcp.tool()
async def custom_analysis_tool(data_source: str, analysis_type: str, 
                              parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    自定义分析工具
    
    Args:
        data_source: 数据源标识
        analysis_type: 分析类型
        parameters: 分析参数
        
    Returns:
        分析结果字典
    """
    try:
        # 实现具体的分析逻辑
        result = await perform_custom_analysis(data_source, analysis_type, parameters)
        
        return {
            "status": "success",
            "result": result,
            "analysis_type": analysis_type
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "analysis_type": analysis_type
        }

if __name__ == "__main__":
    mcp.run(transport="stdio")
```

#### 2. 注册新服务器
```python
# 在 MCPManager 中注册新服务器
def register_custom_servers(self, params: StreamGenerationParams):
    """注册自定义服务器"""
    custom_servers = [
        {
            "name": "custom_analysis",
            "command": sys.executable,
            "args": [f"{working_dir()}/infra/mcp/manager/server/custom_analysis.py"],
            "link": "stdio",
            "type": "custom",
            "env": {
                "ANALYSIS_CONFIG": json.dumps(params.analysis_config),
            },
            "url": None,
        }
    ]
    
    for server_config in custom_servers:
        self.register_server(**server_config)
```

#### 3. 实现工具核心逻辑
```python
# 在核心模块中实现业务逻辑
async def perform_custom_analysis(data_source: str, analysis_type: str, 
                                 parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行自定义分析的核心逻辑
    
    Implementation:
        1. 验证输入参数
        2. 加载数据源
        3. 执行分析算法
        4. 生成分析报告
        5. 返回结构化结果
    """
    # 数据加载
    data = await load_data_from_source(data_source)
    
    # 分析执行
    if analysis_type == "statistical":
        result = await statistical_analysis(data, parameters)
    elif analysis_type == "predictive":
        result = await predictive_analysis(data, parameters)
    else:
        raise ValueError(f"Unsupported analysis type: {analysis_type}")
    
    return result
```

## 测试和质量保证

### 单元测试框架

```python
import pytest
from unittest.mock import AsyncMock, MagicMock
from infra.mcp.manager.mcp_manager import MCPManager

@pytest.fixture
async def mock_mcp_manager():
    """创建模拟的MCP管理器"""
    manager = MCPManager()
    manager.mcp_servers = {
        "test_server": {
            "params": MagicMock(),
            "tools": None,
            "link": "stdio",
            "url": None,
            "type": "test"
        }
    }
    return manager

@pytest.mark.asyncio
async def test_tool_call_success(mock_mcp_manager):
    """测试工具调用成功场景"""
    # 模拟工具调用
    result = await mock_mcp_manager.call_tool(
        "test_server__test_tool",
        arguments={"param1": "value1"}
    )
    
    assert result is not None
    # 验证结果格式和内容

@pytest.mark.asyncio
async def test_tool_call_failure(mock_mcp_manager):
    """测试工具调用失败场景"""
    with pytest.raises(Exception):
        await mock_mcp_manager.call_tool(
            "nonexistent_server__test_tool",
            arguments={}
        )
```

### 集成测试

```python
@pytest.mark.asyncio
async def test_jupyter_code_execution():
    """测试Jupyter代码执行集成"""
    manager = MCPManager()
    
    # 测试简单代码执行
    result = await manager.call_tool(
        "jupyter__execute_code",
        arguments={"code": "print('Hello, World!')"}
    )
    
    assert result["error"] is False
    assert "Hello, World!" in str(result["outputs"])

@pytest.mark.asyncio
async def test_sql_generation_integration():
    """测试SQL生成集成"""
    manager = MCPManager()
    
    # 测试SQL生成
    result = await manager.call_tool(
        "generate_sql__generate_sql",
        arguments={
            "params": {
                "question": "查询用户表中的所有记录",
                "app_id": "test_app",
                "db_info": '[{"DbName": "test_db", "TableList": ["users"]}]'
            }
        }
    )
    
    assert "sql" in result
    assert "SELECT" in result["sql"].upper()
```

## 监控和运维

### 监控仪表板

```mermaid
graph TB
    subgraph "📊 MCP监控仪表板"
        Overview[总览面板]
        ServerStatus[服务器状态]
        ToolMetrics[工具指标]
        PerformancePanel[性能面板]
    end
    
    subgraph "📈 关键指标"
        QPS[每秒查询数]
        ErrorRate[错误率]
        AvgLatency[平均延迟]
        ActiveSessions[活跃会话]
    end
    
    subgraph "🚨 告警规则"
        HighErrorRate[高错误率告警]
        SlowResponse[响应缓慢告警]
        ServerDown[服务器宕机告警]
        ResourceExhaustion[资源耗尽告警]
    end
    
    Overview --> QPS
    ServerStatus --> ErrorRate
    ToolMetrics --> AvgLatency
    PerformancePanel --> ActiveSessions
    
    QPS --> HighErrorRate
    ErrorRate --> SlowResponse
    AvgLatency --> ServerDown
    ActiveSessions --> ResourceExhaustion
```

### 运维脚本

```bash
#!/bin/bash
# MCP服务器健康检查脚本

check_mcp_health() {
    local server_name=$1
    local server_url=$2
    
    echo "检查 $server_name 服务器状态..."
    
    # 检查进程状态
    if pgrep -f "$server_name" > /dev/null; then
        echo "✅ $server_name 进程运行正常"
    else
        echo "❌ $server_name 进程未运行"
        return 1
    fi
    
    # 检查端口监听
    if netstat -tuln | grep ":8900" > /dev/null; then
        echo "✅ $server_name 端口监听正常"
    else
        echo "❌ $server_name 端口未监听"
        return 1
    fi
    
    # 检查工具调用
    python3 -c "
import asyncio
from infra.mcp.basic import mcp_call_tool

async def test_call():
    try:
        result = await mcp_call_tool('$server_url', 'test_tool', {})
        print('✅ 工具调用测试成功')
        return True
    except Exception as e:
        print(f'❌ 工具调用测试失败: {e}')
        return False

success = asyncio.run(test_call())
exit(0 if success else 1)
"
    
    return $?
}

# 检查所有MCP服务器
servers=(
    "jupyter:http://localhost:8900/sse"
    "generate_sql:http://localhost:8901/sse"
    "nl2code:http://localhost:8902/sse"
    "aisearch:http://localhost:8903/sse"
)

for server in "${servers[@]}"; do
    IFS=':' read -r name url <<< "$server"
    check_mcp_health "$name" "$url"
done
```

## 详细类和方法参考

### MCPManager 详细API

#### 类属性详解
```python
class MCPManager:
    """
    MCP工具管理器的完整实现

    Attributes:
        mcp_servers (Dict[str, Dict[str, Any]]): MCP服务器注册表
            格式: {
                "server_name": {
                    "params": StdioServerParameters | None,
                    "tools": List[Tool] | None,
                    "link": str,  # "stdio" | "sse" | "http"
                    "url": str | None,
                    "type": str | None
                }
            }

        active_sessions (Dict[str, ClientSession]): 活跃客户端会话
            格式: {"server_name": ClientSession实例}

        callback (CallbackDispatcher): 回调分发器
            用途: 处理工具调用的回调事件

        replay_collector (Optional[ReplayDataCollector]): 重放数据收集器
            用途: 收集工具调用数据用于重放和分析

        logger (Logger): 结构化日志记录器
            配置: 包含上下文信息的日志记录

        params (StreamGenerationParams): 流生成参数
            包含: 知识库ID、数据引擎配置等
    """
```

#### 核心方法实现详解

##### _get_session 方法
```python
async def _get_session(self, server_name: str) -> ClientSession:
    """
    获取或创建MCP客户端会话

    Args:
        server_name (str): 服务器名称

    Returns:
        ClientSession: 客户端会话实例

    Implementation Details:
        1. 检查活跃会话缓存
        2. 如果存在且有效，直接返回
        3. 如果不存在或无效，创建新会话
        4. 根据协议类型选择传输方式
        5. 初始化会话连接
        6. 缓存会话以供复用

    Session Lifecycle:
        - 创建: 根据服务器配置创建会话
        - 初始化: 建立连接并完成握手
        - 复用: 缓存会话避免重复创建
        - 清理: 超时或错误时自动清理

    Protocol Handling:
        - stdio: 使用StdioServerParameters启动子进程
        - sse: 连接到SSE端点
        - http: 建立HTTP连接

    Error Recovery:
        - 连接失败时的重试机制
        - 会话异常时的自动重建
        - 资源清理和状态重置
    """
```

##### get_available_tools 方法
```python
async def get_available_tools(self) -> Dict[str, List[str]]:
    """
    获取所有可用工具的列表

    Returns:
        Dict[str, List[str]]: 服务器名称到工具列表的映射

    Process:
        1. 遍历所有注册的服务器
        2. 为每个服务器建立会话
        3. 调用list_tools获取工具列表
        4. 聚合所有工具信息
        5. 处理连接失败的服务器

    Result Format:
        {
            "jupyter": ["execute_code", "load_data_by_sql"],
            "generate_sql": ["generate_sql"],
            "nl2code": ["nl2code"],
            "aisearch": ["aisearch_retrieve"]
        }

    Caching Strategy:
        - 工具列表相对稳定，可以缓存
        - 定期刷新缓存以获取最新信息
        - 服务器重启时自动清理缓存
    """
```

### Jupyter 服务器详细实现

#### KernelManager 集成
```python
def get_kernel_manager_by_kwargs(**kwargs) -> KernelManager:
    """
    根据参数创建内核管理器

    Args:
        **kwargs: 包含执行模式和配置的参数

    Returns:
        KernelManager: 配置好的内核管理器实例

    Execution Modes:
        1. Ray模式:
           - 连接到Ray集群
           - 分布式执行支持
           - 资源调度优化
           - 故障转移能力

        2. 本地模式:
           - 本地Jupyter内核
           - 直接进程管理
           - 简单部署
           - 调试友好

    Ray Configuration:
        - ray_address: Ray集群地址
        - namespace: 命名空间隔离
        - runtime_env: 运行时环境配置
        - dependencies: Python包依赖

    Environment Variables:
        - CONF_PATH: 配置文件路径
        - LOG_PATH: 日志文件路径
        - WORKING_DIR: 工作目录
        - PYTHONPATH: Python模块路径
        - OMP_NUM_THREADS: OpenMP线程数
    """
```

#### 数据加载器详解
```python
def get_code_by_engine_type(engine_type: str, mcp_url: str,
                           kernel_name: str, data_engine_name: str,
                           sql: str) -> Tuple[str, List[str]]:
    """
    根据引擎类型生成数据加载代码

    Args:
        engine_type: 数据引擎类型 (DLC, TCHouseD)
        mcp_url: MCP服务URL
        kernel_name: 内核名称
        data_engine_name: 数据引擎名称
        sql: SQL查询语句

    Returns:
        Tuple[str, List[str]]: (生成的代码, 所需包列表)

    Engine Type Handling:
        1. DLC + DLC内核:
           - 直接使用Spark SQL
           - 无需额外包依赖
           - 高性能执行

        2. DLC + 非DLC内核:
           - 通过MCP调用DLC服务
           - 需要mcp包依赖
           - 网络调用开销

        3. TCHouseD:
           - 通过MCP调用TCHouseD服务
           - 需要mcp包依赖
           - 支持复杂查询

    Code Templates:
        每种引擎类型都有对应的代码模板，确保：
        - 正确的数据加载逻辑
        - 统一的输出格式
        - 完善的错误处理
        - 性能监控集成
    """
```

### NL2SQL 核心实现

#### SQLGenerator 类详解
```python
class SQLGenerator:
    """
    SQL生成器核心实现

    Generation Strategy:
        1. 多阶段生成: 关键词提取 → 上下文构建 → SQL生成
        2. 多候选选择: 生成多个候选SQL，选择最优
        3. 语义验证: 确保生成的SQL符合语义要求
        4. 性能优化: 自动添加索引提示和查询优化

    Attributes:
        question (str): 自然语言问题
        metadata (Metadata): 包含追踪信息的元数据
        database_info (DatabaseInfo): 数据库配置和模式信息
    """

    def _get_database_context(self) -> Dict[str, Any]:
        """
        构建数据库上下文信息

        Returns:
            Dict[str, Any]: 数据库上下文，包含：
                - engine: 数据引擎名称
                - datasource_name: 数据源名称
                - dbname: 数据库名称
                - tables: 表名集合
                - sampling_enabled: 是否启用采样

        Context Building:
            1. 验证数据库连接信息
            2. 检查表的存在性
            3. 获取表的基本统计信息
            4. 构建查询上下文
        """

    def _get_tables_ddl(self, database_context: Dict[str, Any]) -> Dict[str, str]:
        """
        获取表的DDL定义

        Args:
            database_context: 数据库上下文信息

        Returns:
            Dict[str, str]: 表名到DDL的映射

        DDL Collection:
            1. 连接到数据库
            2. 查询表结构信息
            3. 生成CREATE TABLE语句
            4. 包含列类型和约束信息
            5. 添加索引和外键信息

        Optimization:
            - 并行获取多个表的DDL
            - 缓存DDL信息避免重复查询
            - 压缩DDL内容减少传输
        """

    def _select_sql(self, lsh: LSH, candidates: List[SQLGenerateData],
                   ddl: Dict[str, str], keywords: List[str],
                   db_schema: Dict[str, List[Column]]) -> SQLGenerateData:
        """
        从候选SQL中选择最优的一个

        Args:
            lsh: 局部敏感哈希，用于相似度计算
            candidates: 候选SQL列表
            ddl: 表DDL定义
            keywords: 提取的关键词
            db_schema: 数据库模式信息

        Returns:
            SQLGenerateData: 选择的最优SQL

        Selection Criteria:
            1. 语义相似度: 与问题的语义匹配程度
            2. 语法正确性: SQL语法的正确性
            3. 执行效率: 预估的查询性能
            4. 完整性: 是否完整回答问题

        Selection Process:
            1. 过滤无效的候选SQL
            2. 计算语义相似度得分
            3. 验证SQL语法正确性
            4. 评估查询复杂度
            5. 综合评分选择最优
        """
```

### NL2Code 核心实现

#### 场景检测和代码生成
```python
def detect_scenario(user_instruction: str, data_schema: str) -> str:
    """
    检测数据科学场景类型

    Args:
        user_instruction: 用户指令
        data_schema: 数据模式描述

    Returns:
        str: 检测到的场景类型

    Scenario Types:
        - summary_stats: 描述性统计分析
        - distribution_analysis: 数据分布分析
        - correlation_analysis: 相关性分析
        - outlier_detection: 异常值检测
        - data_preprocessing: 数据预处理
        - feature_engineering: 特征工程
        - machine_learning: 机器学习建模
        - visualization: 数据可视化
        - general: 通用数据处理

    Detection Logic:
        1. 关键词匹配: 识别特定的数据科学术语
        2. 意图分析: 理解用户的分析意图
        3. 数据类型分析: 根据数据模式推断适合的分析类型
        4. 复杂度评估: 评估任务的复杂程度
    """

def code_generator_by_scenario(context: str, user_instruction: str,
                              model_name: str, language: str,
                              scenario_enhancement: str) -> str:
    """
    基于场景的代码生成器

    Args:
        context: 编程上下文信息
        user_instruction: 用户指令
        model_name: LLM模型名称
        language: 用户语言
        scenario_enhancement: 场景增强信息

    Returns:
        str: 生成的代码和推理过程

    Generation Process:
        1. 场景特定提示词构建
        2. 上下文信息整合
        3. LLM代码生成调用
        4. 结果解析和验证
        5. 代码优化和修复

    Scenario Enhancement:
        - 为每个场景提供专门的提示词模板
        - 包含最佳实践和常见模式
        - 自动添加错误处理代码
        - 优化代码可读性和性能
    """
```

### AI 搜索核心实现

#### 检索管道详解
```python
def retrieve_by_score(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于评分的文档检索实现

    Args:
        params: 检索参数，包含：
            - question: 查询问题
            - app_id: 应用ID
            - recall_num: 召回数量
            - knowledge_base_ids: 知识库ID列表

    Returns:
        Dict[str, Any]: 检索结果

    Retrieval Pipeline:
        1. 查询预处理:
           - 文本清洗和标准化
           - 停用词过滤
           - 同义词扩展

        2. 向量化编码:
           - 使用预训练的文本编码模型
           - 生成查询向量表示
           - 支持多语言编码

        3. 相似度搜索:
           - 在Elasticsearch中执行向量搜索
           - 支持混合搜索策略
           - 可配置的相似度阈值

        4. 结果重排序:
           - 使用专门的重排序模型
           - 考虑查询上下文相关性
           - 提高结果质量

        5. 后处理:
           - 结果去重和聚合
           - 格式化输出
           - 添加元数据信息
    """

def search_elasticsearch_by_score(question: str, app_id: str,
                                 knowledge_base_ids: List[str],
                                 recall_num: int = 10) -> List[Dict[str, Any]]:
    """
    在Elasticsearch中执行基于评分的搜索

    Args:
        question: 查询问题
        app_id: 应用标识
        knowledge_base_ids: 知识库ID列表
        recall_num: 召回文档数量

    Returns:
        List[Dict[str, Any]]: 搜索结果列表

    Search Strategy:
        1. 构建Elasticsearch查询DSL
        2. 设置相关性评分算法
        3. 配置字段权重
        4. 执行搜索请求
        5. 解析搜索结果

    Query DSL Structure:
        - bool查询: 组合多个查询条件
        - match查询: 文本匹配
        - vector查询: 向量相似度
        - filter查询: 过滤条件
        - boost: 字段权重调整
    """
```

### 广告生成系统详解

#### 状态管理
```python
class AdGenerationState:
    """
    广告生成状态管理器

    Attributes:
        app_info (Dict[str, Any]): 应用信息
            - name: 应用名称
            - score: 应用评分
            - icon_url: 图标URL
            - features: 特性列表
            - description: 应用描述

        ad_type (str): 广告类型 ("horizontal" | "vertical")
        slogan (str): 广告语
        bg_prompt (str): 背景图生成提示词
        use_prompt (str): 使用场景图生成提示词
        bg_url (str): 背景图URL
        use_url (str): 使用场景图URL

        display_elements (Dict[str, bool]): 显示元素控制
            - icon: 是否显示图标
            - rating: 是否显示评分
            - features: 是否显示特性
            - slogan: 是否显示广告语
    """

    def initialize(self, app_info: dict):
        """
        初始化广告生成状态

        Args:
            app_info: 应用信息字典

        Initialization Process:
            1. 存储应用基础信息
            2. 设置广告类型
            3. 生成初始广告语
            4. 创建图片生成提示词
            5. 生成初始图片URL
            6. 配置显示元素

        Content Generation:
            - 广告语: 基于应用特性生成吸引人的文案
            - 背景提示词: 根据应用类型生成背景描述
            - 使用场景提示词: 描述应用的使用场景
        """

    def get_size(self, image_type: str) -> str:
        """
        获取不同图片类型的尺寸规格

        Args:
            image_type: 图片类型 ("bg" | "use")

        Returns:
            str: 图片尺寸字符串 (如 "1280x720")

        Size Specifications:
            横版广告 (horizontal):
            - 背景图: 1280x720 (16:9比例)
            - 使用场景图: 1152x864 (4:3比例)

            竖版广告 (vertical):
            - 背景图: 768x1280 (9:16比例)
            - 使用场景图: 1152x864 (4:3比例)
        """
```

#### 图像生成和布局设计
```python
def horizontal_layout_design(state: dict, font) -> Image.Image:
    """
    横版广告布局设计

    Args:
        state: 广告状态字典
        font: 字体对象

    Returns:
        Image.Image: 设计完成的广告图像

    Layout Elements:
        1. 背景图层:
           - 全屏背景图片
           - 尺寸: 1280x720

        2. 使用场景图层:
           - 位置: (150, 150)
           - 尺寸: 576x432
           - 透明度支持

        3. 应用图标:
           - 位置: (935, 150)
           - 尺寸: 100x100
           - 圆角处理

        4. 文本元素:
           - 应用名称: 位置(935, 260)，字体大小30
           - 评分显示: 位置(915, 330)，星级评分
           - 广告语: 位置(930, 360)，字体大小18

    Design Principles:
        - 视觉层次清晰
        - 信息密度适中
        - 品牌元素突出
        - 可读性优先
    """

def vertical_layout_design(state: dict, font) -> Image.Image:
    """
    竖版广告布局设计

    Layout Strategy:
        - 垂直排列布局
        - 半透明蒙版提高可读性
        - 居中对齐设计
        - 移动端适配优化

    Element Positioning:
        - 背景图: 全屏显示
        - 蒙版: 距离边缘50px
        - 使用场景图: 居中显示
        - 文本元素: 垂直居中排列
    """
```

## 工具开发最佳实践

### 1. 工具接口设计原则

```python
# ✅ 良好的工具设计
@mcp.tool()
async def well_designed_tool(
    # 必需参数放在前面
    primary_input: str,
    # 可选参数有默认值
    options: Optional[Dict[str, Any]] = None,
    # 配置参数有合理默认值
    timeout: int = 300
) -> Dict[str, Any]:
    """
    设计良好的工具示例

    Args:
        primary_input: 主要输入参数，清晰的描述
        options: 可选配置参数，提供默认值
        timeout: 超时设置，有合理的默认值

    Returns:
        标准化的结果格式，包含状态和数据

    Design Principles:
        1. 单一职责: 每个工具专注一个功能
        2. 参数清晰: 参数名称和类型明确
        3. 错误处理: 完善的异常处理机制
        4. 文档完整: 详细的docstring说明
        5. 结果标准: 统一的返回格式
    """
    try:
        # 参数验证
        if not primary_input:
            raise ValueError("primary_input不能为空")

        # 设置默认值
        options = options or {}

        # 执行核心逻辑
        result = await execute_core_logic(primary_input, options, timeout)

        # 返回标准格式
        return {
            "status": "success",
            "data": result,
            "metadata": {
                "execution_time": time.time(),
                "tool_version": "1.0.0"
            }
        }

    except Exception as e:
        logger.error(f"工具执行失败: {e}")
        return {
            "status": "error",
            "error_message": str(e),
            "error_type": type(e).__name__
        }
```

### 2. 异步编程最佳实践

```python
# ✅ 正确的异步工具实现
@mcp.tool()
async def async_data_processor(data_source: str,
                              processing_options: Dict[str, Any]) -> Dict[str, Any]:
    """异步数据处理工具"""

    # 使用异步上下文管理器
    async with get_data_connection(data_source) as conn:
        # 并行处理多个数据块
        tasks = []
        for chunk in data_chunks:
            task = asyncio.create_task(process_chunk(chunk, processing_options))
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果和异常
        successful_results = []
        errors = []

        for result in results:
            if isinstance(result, Exception):
                errors.append(str(result))
            else:
                successful_results.append(result)

        return {
            "status": "success" if not errors else "partial_success",
            "results": successful_results,
            "errors": errors,
            "processed_count": len(successful_results)
        }

# ❌ 错误的同步实现
@mcp.tool()
async def bad_sync_tool(data_list: List[str]) -> Dict[str, Any]:
    """错误的同步实现示例"""
    results = []
    for data in data_list:
        # 错误：在异步函数中使用同步阻塞调用
        result = sync_process_data(data)  # 这会阻塞事件循环
        results.append(result)
    return {"results": results}
```

### 3. 错误处理和重试机制

```python
import asyncio
import random
from typing import Callable, Any

async def retry_with_exponential_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0
) -> Any:
    """
    指数退避重试机制

    Args:
        func: 要重试的异步函数
        max_retries: 最大重试次数
        base_delay: 基础延迟时间
        max_delay: 最大延迟时间
        backoff_factor: 退避因子

    Returns:
        函数执行结果

    Retry Strategy:
        - 第1次重试: base_delay秒后
        - 第2次重试: base_delay * backoff_factor秒后
        - 第3次重试: base_delay * backoff_factor^2秒后
        - 添加随机抖动避免雷群效应
    """
    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e

            if attempt == max_retries:
                raise e

            # 计算延迟时间
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            # 添加随机抖动 (±25%)
            jitter = delay * 0.25 * (2 * random.random() - 1)
            actual_delay = delay + jitter

            logger.warning(f"Attempt {attempt + 1} failed: {e}. "
                          f"Retrying in {actual_delay:.2f}s")
            await asyncio.sleep(actual_delay)

    raise last_exception
```

## 监控和可观测性

### MCP 指标收集

```python
from prometheus_client import Counter, Histogram, Gauge, Summary
import time

# 定义MCP相关指标
MCP_TOOL_CALLS = Counter('mcp_tool_calls_total', 'Total MCP tool calls',
                        ['server_name', 'tool_name', 'status'])
MCP_CALL_DURATION = Histogram('mcp_call_duration_seconds', 'MCP call duration',
                             ['server_name', 'tool_name'])
MCP_ACTIVE_SESSIONS = Gauge('mcp_active_sessions', 'Number of active MCP sessions',
                           ['server_name'])
MCP_ERROR_RATE = Summary('mcp_error_rate', 'MCP error rate by server',
                        ['server_name'])

class MCPMetricsCollector:
    """MCP指标收集器"""

    def __init__(self):
        self.call_start_times = {}

    def record_tool_call_start(self, server_name: str, tool_name: str, call_id: str):
        """记录工具调用开始"""
        self.call_start_times[call_id] = time.time()
        MCP_ACTIVE_SESSIONS.labels(server_name=server_name).inc()

    def record_tool_call_end(self, server_name: str, tool_name: str,
                           call_id: str, status: str):
        """记录工具调用结束"""
        if call_id in self.call_start_times:
            duration = time.time() - self.call_start_times[call_id]
            MCP_CALL_DURATION.labels(server_name=server_name, tool_name=tool_name).observe(duration)
            del self.call_start_times[call_id]

        MCP_TOOL_CALLS.labels(server_name=server_name, tool_name=tool_name, status=status).inc()
        MCP_ACTIVE_SESSIONS.labels(server_name=server_name).dec()

        if status == "error":
            MCP_ERROR_RATE.labels(server_name=server_name).observe(1)
        else:
            MCP_ERROR_RATE.labels(server_name=server_name).observe(0)
```

### 链路追踪集成

```python
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode

class MCPTracer:
    """MCP链路追踪器"""

    def __init__(self):
        self.tracer = trace.get_tracer(__name__)

    async def trace_tool_call(self, server_name: str, tool_name: str,
                             arguments: Dict[str, Any], func: Callable):
        """追踪工具调用"""
        with self.tracer.start_as_current_span(f"mcp.{server_name}.{tool_name}") as span:
            # 设置span属性
            span.set_attribute("mcp.server_name", server_name)
            span.set_attribute("mcp.tool_name", tool_name)
            span.set_attribute("mcp.arguments_count", len(arguments))

            try:
                # 执行工具调用
                result = await func()

                # 记录成功状态
                span.set_status(Status(StatusCode.OK))
                span.set_attribute("mcp.result_size", len(str(result)))

                return result

            except Exception as e:
                # 记录错误状态
                span.set_status(Status(StatusCode.ERROR, str(e)))
                span.record_exception(e)
                raise
```

## 部署和运维指南

### MCP 服务器部署架构

```mermaid
graph TB
    subgraph "🌐 负载均衡层"
        LB[负载均衡器]
        HealthCheck[健康检查]
    end

    subgraph "🔧 MCP服务集群"
        Jupyter1[Jupyter服务器1]
        Jupyter2[Jupyter服务器2]
        NL2SQL1[NL2SQL服务器1]
        NL2SQL2[NL2SQL服务器2]
        NL2Code1[NL2Code服务器1]
        AISearch1[AI搜索服务器1]
    end

    subgraph "📊 数据服务层"
        DLCCluster[DLC集群]
        TCHouseDCluster[TCHouseD集群]
        ElasticsearchCluster[ES集群]
    end

    subgraph "🔄 计算资源层"
        RayCluster[Ray计算集群]
        JupyterKernels[Jupyter内核池]
        GPUNodes[GPU计算节点]
    end

    LB --> HealthCheck
    HealthCheck --> Jupyter1
    HealthCheck --> Jupyter2
    HealthCheck --> NL2SQL1
    HealthCheck --> NL2SQL2
    HealthCheck --> NL2Code1
    HealthCheck --> AISearch1

    Jupyter1 --> RayCluster
    Jupyter2 --> JupyterKernels
    NL2SQL1 --> DLCCluster
    NL2SQL2 --> TCHouseDCluster
    AISearch1 --> ElasticsearchCluster

    NL2Code1 --> GPUNodes
```

### 运维自动化脚本

```bash
#!/bin/bash
# MCP服务器运维脚本

# 服务器配置
SERVERS=(
    "jupyter:8900"
    "generate_sql:8901"
    "nl2code:8902"
    "aisearch:8903"
)

# 健康检查函数
check_server_health() {
    local server_name=$1
    local port=$2

    echo "检查 $server_name 服务器 (端口: $port)..."

    # 检查端口监听
    if ! netstat -tuln | grep ":$port" > /dev/null; then
        echo "❌ $server_name 端口 $port 未监听"
        return 1
    fi

    # 检查HTTP响应
    if ! curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "❌ $server_name 健康检查失败"
        return 1
    fi

    echo "✅ $server_name 运行正常"
    return 0
}

# 重启服务器函数
restart_server() {
    local server_name=$1

    echo "重启 $server_name 服务器..."

    # 停止服务器
    pkill -f "$server_name"
    sleep 5

    # 启动服务器
    python3 "infra/mcp/manager/server/$server_name.py" &

    # 等待启动完成
    sleep 10

    echo "$server_name 重启完成"
}

# 主检查循环
main() {
    echo "开始MCP服务器健康检查..."

    for server in "${SERVERS[@]}"; do
        IFS=':' read -r name port <<< "$server"

        if ! check_server_health "$name" "$port"; then
            echo "⚠️ $name 服务器异常，尝试重启..."
            restart_server "$name"

            # 重新检查
            sleep 15
            if check_server_health "$name" "$port"; then
                echo "✅ $name 服务器重启成功"
            else
                echo "❌ $name 服务器重启失败，需要人工介入"
                # 发送告警通知
                send_alert "$name 服务器重启失败"
            fi
        fi
    done

    echo "MCP服务器健康检查完成"
}

# 发送告警通知
send_alert() {
    local message=$1
    # 实现告警通知逻辑
    echo "告警: $message" | mail -s "MCP服务器告警" <EMAIL>
}

# 执行主函数
main "$@"
```

---

*MCP技术文档版本: v1.0*
*最后更新: 2025-08-14*
*维护团队: Intellix MCP Team*

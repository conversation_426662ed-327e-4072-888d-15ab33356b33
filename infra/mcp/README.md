# MCP (Model Context Protocol) 系统文档

## 📚 文档导航

欢迎来到 Intellix Data Science Agent 的 MCP (Model Context Protocol) 系统文档中心。MCP 是我们系统的核心工具协议层，负责管理和协调各种专业化工具服务。

### 📖 文档结构

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [🏗️ 架构设计文档](./MCP_ARCHITECTURE_DESIGN.md) | 系统整体架构、设计模式和技术选型 | 架构师、技术负责人 |
| [📋 技术文档](./MCP_TECHNICAL_DOCUMENTATION.md) | 详细的技术实现、类和方法说明 | 开发工程师、维护人员 |
| [📚 API 参考文档](./MCP_API_REFERENCE.md) | 完整的API接口文档和使用说明 | 开发工程师、集成开发者 |
| [👨‍💻 开发者指南](./MCP_DEVELOPER_GUIDE.md) | 开发环境设置、最佳实践和调试指南 | 新手开发者、贡献者 |

## 🎯 MCP 系统概述

MCP (Model Context Protocol) 是一个基于微服务架构的工具协议系统，提供以下核心能力：

### 🔧 核心功能

- **🐍 代码执行**: 通过 Jupyter 内核执行 Python 代码
- **🗄️ SQL 生成**: 将自然语言转换为 SQL 查询语句
- **💻 代码生成**: 基于自然语言生成可执行的 Python 代码
- **🔍 智能检索**: AI 驱动的文档和知识库检索
- **🎨 广告生成**: 自动化的广告图像和文案生成
- **📊 数据加载**: 多种数据源的统一数据加载接口

### 🏗️ 系统架构

```mermaid
graph TB
    subgraph "🎯 应用层"
        DataScienceAgent[数据科学代理]
        ChatInterface[聊天接口]
        WebAPI[Web API]
    end
    
    subgraph "🔌 MCP协议层"
        MCPManager[MCP管理器]
        CallbackDispatcher[回调分发器]
        SessionManager[会话管理器]
    end
    
    subgraph "🛠️ 工具服务层"
        JupyterServer[Jupyter服务器]
        NL2SQLServer[NL2SQL服务器]
        NL2CodeServer[NL2Code服务器]
        AISearchServer[AI搜索服务器]
        GenerateAdServer[广告生成服务器]
    end
    
    subgraph "🔗 传输层"
        StdioTransport[Stdio传输]
        SSETransport[SSE传输]
        HTTPTransport[HTTP传输]
    end
    
    DataScienceAgent --> MCPManager
    ChatInterface --> MCPManager
    WebAPI --> MCPManager
    
    MCPManager --> JupyterServer
    MCPManager --> NL2SQLServer
    MCPManager --> NL2CodeServer
    MCPManager --> AISearchServer
    MCPManager --> GenerateAdServer
    
    JupyterServer --> StdioTransport
    NL2SQLServer --> SSETransport
    AISearchServer --> HTTPTransport
```

## 🚀 快速开始

### 基本使用

```python
from infra.mcp.manager.mcp_manager import MCPManager
from infra.mcp.manager.stream_generation_params import StreamGenerationParams

# 1. 创建MCP管理器
manager = MCPManager()

# 2. 配置参数
params = StreamGenerationParams(
    knowledge_base_ids=["kb_001"],
    data_engine_name="test-engine"
)

# 3. 注册服务器
manager.register_default_servers(params)

# 4. 执行代码
result = await manager.call_tool(
    "jupyter__execute_code",
    arguments={"code": "import pandas as pd\nprint('Hello, MCP!')"}
)

# 5. 生成SQL
sql_result = await manager.call_tool(
    "generate_sql__generate_sql",
    arguments={
        "params": {
            "question": "查询用户表中的所有记录",
            "app_id": "test_app",
            "db_info": '[{"DbName": "test_db", "TableList": ["users"]}]'
        }
    }
)

# 6. 生成代码
code_result = await manager.call_tool(
    "nl2code__nl2code",
    arguments={
        "params": {
            "user_instruction": "计算数据的描述性统计",
            "data_type": "DataFrame",
            "data_schema": "columns: [id, name, age, salary]"
        }
    }
)
```

### 工具调用示例

#### 1. 代码执行工具
```python
# 执行简单的Python代码
result = await manager.call_tool(
    "jupyter__execute_code",
    arguments={
        "code": """
import numpy as np
import matplotlib.pyplot as plt

# 生成示例数据
data = np.random.normal(0, 1, 1000)

# 创建直方图
plt.figure(figsize=(10, 6))
plt.hist(data, bins=30, alpha=0.7)
plt.title('正态分布直方图')
plt.xlabel('值')
plt.ylabel('频率')
plt.show()

print(f'数据均值: {np.mean(data):.3f}')
print(f'数据标准差: {np.std(data):.3f}')
        """,
        "required_packages": ["numpy", "matplotlib"]
    }
)
```

#### 2. SQL生成工具
```python
# 生成复杂的SQL查询
sql_result = await manager.call_tool(
    "generate_sql__generate_sql",
    arguments={
        "params": {
            "question": "查询每个部门的平均薪资，按薪资降序排列，只显示前10个部门",
            "app_id": "hr_system",
            "sub_account_uin": "user123",
            "trace_id": "trace_001",
            "data_engine_name": "hr_data_engine",
            "db_info": '[{"CatalogName": "HRCatalog", "DbName": "hr_db", "TableList": ["employees", "departments"]}]',
            "is_sampling": False,
            "mcp_url": {"DLC": "http://dlc-service:8080/sse"},
            "type": "DLC",
            "record_id": "query_001"
        }
    }
)

print(f"生成的SQL: {sql_result['sql']}")
print(f"推理过程: {sql_result['reasoning']}")
```

#### 3. 代码生成工具
```python
# 生成数据分析代码
code_result = await manager.call_tool(
    "nl2code__nl2code",
    arguments={
        "params": {
            "scenario": "correlation_analysis",
            "user_instruction": "分析销售数据中各个特征之间的相关性，并生成热力图",
            "env_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
            "global_vars": {"df": "sales_dataframe"},
            "data_type": "DataFrame",
            "data_schema": "columns: [date, product_id, sales_amount, region, customer_type]",
            "model_name": "DeepSeek-V3-0324"
        }
    }
)

print(f"生成的代码:\n{code_result['python_code']}")
print(f"所需包: {code_result['required_packages']}")
```

#### 4. AI搜索工具
```python
# 智能文档检索
search_result = await manager.call_tool(
    "aisearch__aisearch_retrieve",
    arguments={
        "question": "如何使用pandas进行数据清洗和预处理？",
        "recall_num": 5
    }
)

for doc in search_result["aisearch"]:
    print(f"文档内容: {doc['content'][:200]}...")
    print(f"相关性评分: {doc['score']}")
    print(f"重排序评分: {doc['rerank_score']}")
    print("---")
```

## 🔧 开发指南

### 创建新工具的步骤

1. **📝 定义工具接口**: 在 `infra/mcp/manager/server/` 目录下创建新的服务器文件
2. **🧠 实现核心逻辑**: 在相应的核心模块中实现业务逻辑
3. **🔌 注册服务器**: 在 `MCPManager` 中注册新的服务器
4. **🧪 编写测试**: 创建单元测试和集成测试
5. **📚 更新文档**: 更新API文档和使用说明

### 开发最佳实践

- ✅ **使用异步编程**: 所有工具都应该是异步的
- ✅ **完善错误处理**: 捕获和处理所有可能的异常
- ✅ **参数验证**: 使用 Pydantic 进行参数验证
- ✅ **日志记录**: 记录详细的执行日志
- ✅ **性能监控**: 添加性能指标收集
- ✅ **文档完整**: 提供详细的 docstring 和使用示例

## 🛠️ 工具列表

### 核心工具

| 工具名称 | 服务器 | 功能描述 | 状态 |
|----------|--------|----------|------|
| `execute_code` | jupyter | 执行Python代码 | ✅ 稳定 |
| `load_data_by_sql` | jupyter | SQL数据加载 | ✅ 稳定 |
| `generate_sql` | generate_sql | 自然语言转SQL | ✅ 稳定 |
| `nl2code` | nl2code | 自然语言转代码 | ✅ 稳定 |
| `aisearch_retrieve` | aisearch | AI智能检索 | ✅ 稳定 |
| `generate_horizontal_ad` | generate_ad | 横版广告生成 | ✅ 稳定 |
| `generate_vertical_ad` | generate_ad | 竖版广告生成 | ✅ 稳定 |

### 辅助工具

| 工具名称 | 服务器 | 功能描述 | 状态 |
|----------|--------|----------|------|
| `install_packages` | jupyter | 安装Python包 | ✅ 稳定 |
| `get_kernel_info` | jupyter | 获取内核信息 | ✅ 稳定 |
| `extract_app_info` | generate_ad | 提取应用信息 | ✅ 稳定 |
| `regenerate_slogan` | generate_ad | 重新生成广告语 | ✅ 稳定 |
| `select_tables` | select_tables | 智能表选择 | 🚧 开发中 |

## 🔍 故障排除

### 常见问题

#### 1. 服务器连接失败
```bash
# 检查服务器状态
python -c "
from infra.mcp.manager.mcp_manager import MCPManager
import asyncio

async def check():
    manager = MCPManager()
    tools = await manager.get_available_tools()
    print('可用工具:', tools)

asyncio.run(check())
"
```

#### 2. 工具调用超时
```python
# 增加超时时间
result = await manager.call_tool(
    "jupyter__execute_code",
    arguments={
        "code": "# 长时间运行的代码",
        "timeout": 600  # 增加到10分钟
    }
)
```

#### 3. 内存不足错误
```python
# 使用数据采样
result = await manager.call_tool(
    "jupyter__load_data_by_sql",
    arguments={
        "sql": "SELECT * FROM large_table LIMIT 10000",  # 限制数据量
        "engine_type": "dlc",
        # ... 其他参数
    }
)
```

### 调试技巧

#### 启用调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 创建调试管理器
from infra.mcp.debug import MCPDebugger
debugger = MCPDebugger(manager)

# 使用调试工具调用
result = await debugger.debug_tool_call(
    "jupyter__execute_code",
    arguments={"code": "print('debug test')"}
)
```

#### 查看服务器状态
```python
# 检查所有服务器状态
await debugger.list_server_status()

# 诊断特定服务器
await debugger.diagnose_connection_issues("jupyter")
```

## 📊 性能监控

### 监控指标

MCP 系统提供丰富的监控指标：

- **📈 业务指标**: 工具调用次数、成功率、响应时间
- **🖥️ 系统指标**: CPU、内存、网络使用率
- **🔧 服务指标**: 服务器可用性、连接池状态
- **🔒 安全指标**: 认证失败次数、异常访问检测

### Prometheus 指标

```python
# 主要监控指标
mcp_tool_calls_total{server_name, tool_name, status}
mcp_tool_call_duration_seconds{server_name, tool_name}
mcp_active_sessions{server_name}
mcp_error_rate{server_name}
mcp_connection_pool_size{server_name}
mcp_cache_hit_rate{cache_type}
```

### Grafana 仪表板

访问 Grafana 仪表板查看实时监控数据：
- **MCP 总览**: 系统整体健康状况
- **工具性能**: 各工具的性能指标
- **服务器状态**: 各服务器的运行状态
- **错误分析**: 错误趋势和根因分析

## 🔐 安全和权限

### 认证方式

MCP 系统支持多种认证方式：

1. **Token 认证**: 基于 JWT Token 的认证
2. **API Key 认证**: 基于 API Key 的认证
3. **证书认证**: 基于 X.509 证书的认证

### 权限控制

```python
# 角色权限配置示例
ROLE_PERMISSIONS = {
    "admin": ["*"],  # 管理员拥有所有权限
    "data_scientist": [
        "jupyter__*",
        "generate_sql__*",
        "nl2code__*",
        "aisearch__*"
    ],
    "analyst": [
        "jupyter__execute_code",
        "generate_sql__generate_sql",
        "aisearch__aisearch_retrieve"
    ],
    "guest": [
        "aisearch__aisearch_retrieve"
    ]
}
```

## 🚀 部署指南

### 本地开发部署

```bash
# 1. 启动单个服务器
python infra/mcp/jupyter/server.py

# 2. 启动所有服务器
python scripts/start_all_mcp_servers.py

# 3. 运行测试
pytest tests/mcp/ -v
```

### 生产环境部署

```bash
# 1. 构建Docker镜像
docker build -t mcp-server:latest .

# 2. 部署到Kubernetes
kubectl apply -f k8s/mcp-deployment.yaml

# 3. 检查部署状态
kubectl get pods -l component=mcp

# 4. 查看日志
kubectl logs -f deployment/mcp-jupyter-server
```

### 配置管理

```yaml
# config/mcp_config.yaml
mcp:
  global:
    default_timeout: 300
    max_concurrent_calls: 100
    
  servers:
    jupyter:
      command: "python3"
      args: ["infra/mcp/jupyter/server.py"]
      link: "stdio"
      
  security:
    enable_authentication: true
    token_expiry: 3600
    
  monitoring:
    enable_metrics: true
    metrics_port: 9090
```

## 🧪 测试指南

### 运行测试

```bash
# 运行所有MCP测试
pytest tests/mcp/ -v

# 运行特定测试文件
pytest tests/mcp/test_mcp_manager.py -v

# 运行集成测试
pytest tests/integration/test_mcp_integration.py -v

# 生成测试覆盖率报告
pytest tests/mcp/ --cov=infra/mcp --cov-report=html
```

### 测试环境配置

```bash
# 设置测试环境变量
export MCP_TEST_MODE=true
export MCP_LOG_LEVEL=DEBUG
export MCP_CACHE_DISABLED=true

# 启动测试服务器
python tests/fixtures/test_mcp_server.py &

# 运行测试
pytest tests/mcp/
```

## 📈 性能优化

### 优化建议

1. **连接复用**: 使用连接池减少连接开销
2. **结果缓存**: 缓存常用工具的调用结果
3. **并行执行**: 利用异步编程提高并发性能
4. **资源限制**: 设置合理的资源使用限制
5. **监控告警**: 建立完善的监控和告警机制

### 性能调优参数

```python
# 性能调优配置
PERFORMANCE_CONFIG = {
    "connection_pool_size": 20,      # 连接池大小
    "max_concurrent_calls": 100,     # 最大并发调用数
    "default_timeout": 300,          # 默认超时时间
    "cache_ttl": 3600,              # 缓存生存时间
    "health_check_interval": 30,     # 健康检查间隔
    "retry_max_attempts": 3,         # 最大重试次数
    "retry_backoff_factor": 2.0      # 重试退避因子
}
```

## 🤝 贡献指南

### 贡献流程

1. **🍴 Fork 项目**: 从主仓库 fork 项目到个人仓库
2. **🌿 创建分支**: 创建功能分支进行开发
3. **💻 开发功能**: 按照开发规范实现新功能
4. **🧪 编写测试**: 确保代码覆盖率达到要求
5. **📝 更新文档**: 更新相关文档和API说明
6. **🔍 代码审查**: 提交 Pull Request 进行代码审查
7. **🚀 合并发布**: 审查通过后合并到主分支

### 代码规范

```python
# 代码风格要求
# 1. 使用 Black 进行代码格式化
# 2. 使用 isort 进行导入排序
# 3. 使用 flake8 进行代码检查
# 4. 使用 mypy 进行类型检查

# 示例：符合规范的工具实现
from typing import Dict, Any, Optional
from mcp.server.fastmcp import FastMCP
from common.logger.logger import logger

mcp = FastMCP("Example Server")

@mcp.tool()
async def example_tool(
    input_data: str,
    options: Optional[Dict[str, Any]] = None,
    timeout: int = 300
) -> Dict[str, Any]:
    """
    示例工具实现
    
    Args:
        input_data: 输入数据
        options: 可选配置参数
        timeout: 超时时间
        
    Returns:
        工具执行结果
    """
    try:
        # 参数验证
        if not input_data:
            raise ValueError("input_data不能为空")
        
        # 执行逻辑
        result = await process_data(input_data, options or {})
        
        return {
            "status": "success",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"工具执行失败: {e}")
        return {
            "status": "error",
            "error_message": str(e)
        }
```

## 📞 支持和联系

### 技术支持

- **📧 邮箱**: <EMAIL>
- **💬 Slack**: #mcp-support
- **🐛 问题报告**: [GitHub Issues](https://github.com/intellix/mcp/issues)
- **📖 Wiki**: [内部Wiki](https://wiki.intellix.com/mcp)

### 开发团队

- **架构负责人**: 负责系统架构设计和技术选型
- **核心开发**: 负责核心功能开发和维护
- **测试工程师**: 负责测试用例编写和质量保证
- **运维工程师**: 负责部署、监控和运维

### 版本发布

- **🔄 发布周期**: 每两周发布一个版本
- **🏷️ 版本命名**: 使用语义化版本号 (v1.2.3)
- **📋 变更日志**: 详细记录每个版本的变更内容
- **🔒 稳定性**: 主分支保持稳定，新功能在开发分支

## 📋 路线图

### 近期计划 (Q1 2025)

- [ ] **🔧 工具扩展**: 添加更多数据科学工具
- [ ] **⚡ 性能优化**: 提升工具调用性能
- [ ] **🔒 安全增强**: 加强安全控制和审计
- [ ] **📊 监控完善**: 完善监控和告警体系

### 中期计划 (Q2-Q3 2025)

- [ ] **🌐 多语言支持**: 支持更多编程语言
- [ ] **☁️ 云原生**: 完全云原生化部署
- [ ] **🤖 AI增强**: 集成更多AI能力
- [ ] **📱 移动端**: 支持移动端工具调用

### 长期愿景 (2025年底)

- [ ] **🌍 生态系统**: 建立完整的工具生态系统
- [ ] **🔌 插件市场**: 支持第三方工具插件
- [ ] **🎯 智能化**: 实现智能工具推荐和自动化
- [ ] **🌟 开源**: 部分组件开源贡献社区

---

*欢迎加入 MCP 开发社区，共同构建更强大的工具协议系统！*

*MCP 系统文档版本: v1.0*  
*最后更新: 2025-08-14*  
*维护团队: Intellix MCP Team*
